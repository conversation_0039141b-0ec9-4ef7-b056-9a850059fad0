import { Injectable, inject } from '@angular/core';
import {
  Observable,
  timer,
  switchMap,
  takeWhile,
  map,
  catchError,
  of,
  BehaviorSubject,
  throwError,
} from 'rxjs';
import { FirebaseService, AnalysisJob } from './firebase.service';
import {FirebaseError} from 'firebase/app';

export interface AnalysisProgress {
  captureId: string;
  status: 'PENDING' | 'FINISHED' | 'ERROR';
  progress: number; // 0-100
  message: string;
  job?: AnalysisJob;
  error?: string;
}

@Injectable({
  providedIn: 'root',
})
export class AnalysisPollingService {
  private firebaseService = inject(FirebaseService);
  private activePolls = new Map<string, BehaviorSubject<AnalysisProgress>>();

  /**
   * Start polling for analysis job status
   * @param captureId The capture ID to poll for
   * @param maxDuration Maximum polling duration in milliseconds (default: 5 minutes)
   * @param interval Polling interval in milliseconds (default: 2 seconds)
   */
  startPolling(
    captureId: string,
    maxDuration: number = 5 * 60 * 1000, // 5 minutes
    interval: number = 2000 // 2 seconds
  ): Observable<AnalysisProgress> {
    // If already polling this capture ID, return existing observable
    if (this.activePolls.has(captureId)) {
      return this.activePolls.get(captureId)!.asObservable();
    }

    const progressSubject = new BehaviorSubject<AnalysisProgress>({
      captureId,
      status: 'PENDING',
      progress: 0,
      message: 'Starting analysis...',
    });

    this.activePolls.set(captureId, progressSubject);

    const startTime = Date.now();

    // Create polling observable
    const polling$ = timer(0, interval).pipe(
      switchMap(() =>
        this.firebaseService.getAnalysisJobStatus(captureId).pipe(
          catchError((error) => {
            if ((error as FirebaseError).code === 'functions/not-found') {
              return of(null);
            }
            return throwError(() => new Error(error));
          })
        )
      ),
      map((job: AnalysisJob | null) => {
        const elapsed = Date.now() - startTime;
        const progressPercent = Math.min((elapsed / maxDuration) * 100, 95);

        if (!job) {
          return {
            captureId,
            status: 'PENDING' as const,
            progress: Math.max(progressPercent, 10),
            message: 'Waiting for analysis to start...',
            job: undefined,
          };
        }

        switch (job.status) {
          case 'PENDING':
            return {
              captureId,
              status: 'PENDING' as const,
              progress: Math.max(progressPercent, 25),
              message: 'Analyzing pigeon image...',
              job,
            };

          case 'FINISHED':
            return {
              captureId,
              status: 'FINISHED' as const,
              progress: 100,
              message: 'Analysis complete! Pigeon captured successfully.',
              job,
            };

          case 'ERROR':
            return {
              captureId,
              status: 'ERROR' as const,
              progress: 100,
              message: job.errorMessage || 'Analysis failed',
              job,
              error: job.errorCode,
            };

          default:
            return {
              captureId,
              status: 'PENDING' as const,
              progress: progressPercent,
              message: 'Processing...',
              job,
            };
        }
      }),
      catchError((error) => {
        console.error('Polling error:', error);
        return of({
          captureId,
          status: 'ERROR' as const,
          progress: 100,
          message: 'Failed to check analysis status',
          error: error.message || 'Unknown error',
        });
      }),
      takeWhile((progress) => {
        const shouldContinue =
          progress.status === 'PENDING' && Date.now() - startTime < maxDuration;

        // Update the subject with current progress
        progressSubject.next(progress);

        // If we're done polling, clean up
        if (!shouldContinue) {
          setTimeout(() => {
            this.activePolls.delete(captureId);
            progressSubject.complete();
          }, 1000); // Small delay to ensure final value is emitted
        }

        return shouldContinue;
      }, true) // Include the final value
    );

    // Start the polling
    polling$.subscribe({
      next: (progress) => {
        // Progress updates are handled in takeWhile
      },
      error: (error) => {
        console.error('Polling subscription error:', error);
        progressSubject.error(error);
        this.activePolls.delete(captureId);
      },
    });

    return progressSubject.asObservable();
  }

  /**
   * Stop polling for a specific capture ID
   */
  stopPolling(captureId: string): void {
    const subject = this.activePolls.get(captureId);
    if (subject) {
      subject.complete();
      this.activePolls.delete(captureId);
    }
  }

  /**
   * Stop all active polling
   */
  stopAllPolling(): void {
    this.activePolls.forEach((subject, captureId) => {
      subject.complete();
    });
    this.activePolls.clear();
  }

  /**
   * Get current polling status for a capture ID
   */
  getPollingStatus(captureId: string): AnalysisProgress | null {
    const subject = this.activePolls.get(captureId);
    return subject ? subject.value : null;
  }

  /**
   * Check if currently polling for a capture ID
   */
  isPolling(captureId: string): boolean {
    return this.activePolls.has(captureId);
  }
}
