import { Injectable, inject } from '@angular/core';
import { Auth, signInAnonymously, User } from '@angular/fire/auth';
import { Storage, ref, uploadBytes, getDownloadURL } from '@angular/fire/storage';
import { Functions, httpsCallable } from '@angular/fire/functions';
import { Observable, from, BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';

export interface AnalysisJob {
  captureId: string;
  trainerId: string;
  storageFilePath: string;
  status: 'PENDING' | 'FINISHED' | 'ERROR';
  errorCode?: string;
  errorMessage?: string;
  createdAt: Date;
  updatedAt: Date;
  pigeonId?: string;
}

export interface PigeonWithBasePigeon {
  id: string;
  basePigeonId: string;
  ownerId: string;
  captureId: string;
  gang: string;
  captureDate: Date;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  basePigeon: {
    id: string;
    slug: string;
    name: {
        fr: string;
        en: string;
    };
    distinctiveness: string;
    description: string;
    skin: {
      originalUrl: string;
      smallUrl: string;
    };
  };
}

@Injectable({
  providedIn: 'root'
})
export class FirebaseService {
  private auth = inject(Auth);
  private storage = inject(Storage);
  private functions = inject(Functions);
  
  private currentUser$ = new BehaviorSubject<User | null>(null);
  
  constructor() {
    // Listen to auth state changes
    this.auth.onAuthStateChanged(user => {
      this.currentUser$.next(user);
    });
  }

  // Authentication
  async signInAnonymously(): Promise<User> {
    const result = await signInAnonymously(this.auth);
    return result.user;
  }

  getCurrentUser(): Observable<User | null> {
    return this.currentUser$.asObservable();
  }

  // File upload to Firebase Storage
  async uploadPigeonImage(file: File, latitude: number, longitude: number): Promise<{
    captureId: string;
    storageFilePath: string;
    downloadUrl: string;
  }> {
    const user = this.auth.currentUser;
    if (!user) {
      throw new Error('User must be authenticated to upload files');
    }

    const captureId = uuidv4();
    const trainerId = user.uid;
    const fileName = `${latitude}_${longitude}_${file.name}`;
    const storageFilePath = `shots/${trainerId}/${captureId}/${fileName}`;
    
    const storageRef = ref(this.storage, storageFilePath);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadUrl = await getDownloadURL(snapshot.ref);
    
    return {
      captureId,
      storageFilePath,
      downloadUrl
    };
  }

  // Get analysis job status
  getAnalysisJobStatus(captureId: string): Observable<AnalysisJob | null> {
    const callable = httpsCallable<{ captureId: string }, AnalysisJob | null>(
      this.functions, 
      'getAnalysisJobStatus'
    );
    return from(callable({ captureId })).pipe(
      // Extract the data from the callable result
      map((result: any) => result.data)
    );
  }

  // Get pigeon by capture ID (when analysis is complete)
  getPigeonByCaptureId(captureId: string): Observable<{ pigeon: PigeonWithBasePigeon }> {
    const callable = httpsCallable<{ captureId: string }, { pigeon: PigeonWithBasePigeon }>(
      this.functions,
      'getPigeonByCaptureId'
    );
    return from(callable({ captureId })).pipe(
      map((result: any) => result.data)
    );
  }

  // Get trainer's pigeon deck (all pigeons owned by the trainer)
  getTrainerPigeondex(limit?: number, offset?: number): Observable<{
    pigeons: PigeonWithBasePigeon[];
    total: number;
    hasMore: boolean;
  }> {
    const callable = httpsCallable<
      { limit?: number; offset?: number },
      { pigeons: PigeonWithBasePigeon[]; total: number; hasMore: boolean }
    >(
      this.functions,
      'getTrainerPigeondex'
    );
    return from(callable({ limit, offset })).pipe(
      map((result: any) => result.data)
    );
  }
}
