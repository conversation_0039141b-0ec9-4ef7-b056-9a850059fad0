import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, map } from 'rxjs';

export interface PigeonTraits {
  base_color: "blue" | "ash-red" | "brown" | "other";
  main_pattern: "bar" | "checker" | "t-check" | "barless" | "other";
  is_spread: boolean;
  spread_level: "none" | "partial" | "full";
  is_piebald: boolean;
  piebald_level: "none" | "intermediate" | "heavy" | "full_white";
  piebald_pattern: "none" | "white_patches" | "small_white_spots" | "mottled";
  piebald_intensity: {
    head: "none" | "light" | "intermediate" | "heavy" | "fully_white";
    neck: "none" | "light" | "intermediate" | "heavy" | "fully_white";
    body: "none" | "light" | "intermediate" | "heavy" | "fully_white";
    tail: "none" | "light" | "intermediate" | "heavy" | "fully_white";
  } | null;
  head_pattern: "helmet" | "baldhead" | "none";
  iridescence_level: "none" | "low" | "medium" | "high";
  wing_tip_color: "black" | "dark" | "grey" | "light" | "white";
  tail_color: "black" | "dark" | "light" | "white" | "mixed";
  face_pattern: "uniform_standard" | "mottled" | "weird_spots";
}

export interface PigeonAnalysisResult {
  filename: string;
  looks_like_a_screenshot: number;
  is_bird: boolean;
  species: string;
  is_baby_pigeon: boolean;
  is_dead: boolean;
  distinctiveness: "common" | "unusual" | "rare";
  description: string;
  pigeon_traits: PigeonTraits | null;
}

export interface ClassifiedPigeon {
  filename: string;
  character: string;
  description: string;
  analysis: PigeonAnalysisResult;
  imageUrl: string;
}

export interface CharacterDistribution {
  [character: string]: number;
}

export interface ClassificationReport {
  total_images: number;
  total_unique_characters: number;
  target_characters: number;
  character_distribution: CharacterDistribution;
  examples: ClassifiedPigeon[];
}

@Injectable({
  providedIn: 'root'
})
export class PigeonClassifierService {
  private classificationData$ = new BehaviorSubject<ClassifiedPigeon[]>([]);
  private characterDistribution$ = new BehaviorSubject<CharacterDistribution>({});
  private loading$ = new BehaviorSubject<boolean>(false);

  constructor(private http: HttpClient) {}

  /**
   * Load classification data from the backend
   */
  loadClassificationData(): Observable<ClassificationReport> {
    this.loading$.next(true);

    // For development, we'll create mock data based on the analysis results
    // In production, this would be an API call to your Firebase Functions
    return this.createMockData().pipe(
      map(report => {
        this.classificationData$.next(report.examples);
        this.characterDistribution$.next(report.character_distribution);
        this.loading$.next(false);
        return report;
      })
    );
  }

  /**
   * Create mock data for development
   */
  private createMockData(): Observable<ClassificationReport> {
    // Mock data based on the analysis results structure
    const mockData: ClassificationReport = {
      total_images: 166,
      total_unique_characters: 30,
      target_characters: 50,
      character_distribution: {
        "BLUE_BAR": 36,
        "MOTTLED_FACE": 25,
        "BLUE_CHECKER": 19,
        "NO_IRIDESCENCE": 10,
        "WEIRD_FACE_SPOTS": 9,
        "BLUE_SPREAD_T_CHECK": 7,
        "WHITE_PATCHES_PIEBALD": 7,
        "MOTTLED_PIEBALD": 6,
        "WHITE_TAIL": 6,
        "SMALL_WHITE_SPOTS_PIEBALD": 4,
        "BLUE_SPREAD_OTHER": 4,
        "HIGH_IRIDESCENCE": 3,
        "WHITE_WING_TIPS": 3,
        "WOOD_PIGEON": 3,
        "HELMET_PIGEON": 3,
        "BABY_PIGEON": 2,
        "BLUE_SPREAD_LIGHT_PIEBALD": 2,
        "BALDHEAD_PIGEON": 2,
        "DEAD_PIGEON": 2,
        "DUCK": 2,
        "SEAGULL": 2,
        "MAGPIE": 1,
        "RARE_DISTINCTIVENESS": 1,
        "TURTLEDOVE": 1,
        "OTHER_BIRD": 1,
        "CROW": 1,
        "MIXED_TAIL_COLOR": 1,
        "RAVEN": 1,
        "ROOSTER": 1,
        "ASH_RED_BAR": 1
      },
      examples: this.generateMockExamples()
    };

    return new Observable(observer => {
      // Simulate network delay
      setTimeout(() => {
        observer.next(mockData);
        observer.complete();
      }, 1000);
    });
  }

  private generateMockExamples(): ClassifiedPigeon[] {
    // Generate a comprehensive set of mock examples representing different character types
    const mockExamples: ClassifiedPigeon[] = [];

    const sampleFilenames = [
      "aaron.jpg", "about.jpg", "aaaron.jpg", "acres.jpg", "afternoon.jpg",
      "angle.jpg", "another.jpg", "anything.jpg", "attack.jpg", "automobile.jpg",
      "balance.jpg", "balloon.jpg", "basket.jpg", "become.jpg", "bent.jpg",
      "bernard.jpg", "beside.jpg", "bit.jpg", "blow.jpg", "breeze.jpg",
      "broke.jpg", "burst.jpg", "cabin.jpg", "case.jpg", "center.jpg",
      "chair.jpg", "chapter.jpg", "circus.jpg", "clear.jpg", "common.jpg"
    ];

    const characters = [
      "BLUE_BAR", "BLUE_CHECKER", "MOTTLED_FACE", "WEIRD_FACE_SPOTS",
      "BLUE_SPREAD_T_CHECK", "WHITE_PATCHES_PIEBALD", "MOTTLED_PIEBALD",
      "WHITE_TAIL", "NO_IRIDESCENCE", "HIGH_IRIDESCENCE", "MAGPIE",
      "WOOD_PIGEON", "BABY_PIGEON", "RARE_DISTINCTIVENESS", "BALDHEAD_PIGEON"
    ];

    const distinctiveness = ["common", "unusual", "rare"];
    const baseColors = ["blue", "brown", "ash-red"];
    const patterns = ["bar", "checker", "t-check", "barless", "other"];

    sampleFilenames.forEach((filename, index) => {
      const character = characters[index % characters.length];
      const dist = distinctiveness[index % distinctiveness.length];
      const baseColor = baseColors[index % baseColors.length];
      const pattern = patterns[index % patterns.length];

      mockExamples.push({
        filename,
        character,
        description: this.getCharacterDescription(character),
        imageUrl: `assets/placeholder-pigeon.svg`, // Use placeholder for all images
        analysis: {
          filename,
          looks_like_a_screenshot: 1,
          is_bird: true,
          species: character.includes('MAGPIE') ? 'magpie' :
                   character.includes('WOOD') ? 'wood_pigeon' :
                   character.includes('BABY') ? 'columba_livia' : 'columba_livia',
          is_baby_pigeon: character === 'BABY_PIGEON',
          is_dead: character === 'DEAD_PIGEON',
          distinctiveness: dist as "common" | "unusual" | "rare",
          description: this.generateDescription(character, dist),
          pigeon_traits: character.includes('MAGPIE') || character.includes('WOOD') ? null : {
            base_color: baseColor as "blue" | "ash-red" | "brown" | "other",
            main_pattern: pattern as "bar" | "checker" | "t-check" | "barless" | "other",
            is_spread: character.includes('SPREAD'),
            spread_level: character.includes('SPREAD') ? "full" : "none",
            is_piebald: character.includes('PIEBALD'),
            piebald_level: character.includes('HEAVY') ? "heavy" :
                          character.includes('LIGHT') ? "intermediate" : "none",
            piebald_pattern: character.includes('MOTTLED') ? "mottled" :
                           character.includes('WHITE_PATCHES') ? "white_patches" :
                           character.includes('SMALL_WHITE_SPOTS') ? "small_white_spots" : "none",
            piebald_intensity: character.includes('PIEBALD') ? {
              head: "light",
              neck: "none",
              body: "intermediate",
              tail: "light"
            } : null,
            head_pattern: character.includes('BALDHEAD') ? "baldhead" :
                         character.includes('HELMET') ? "helmet" : "none",
            iridescence_level: character.includes('HIGH_IRIDESCENCE') ? "high" :
                              character.includes('NO_IRIDESCENCE') ? "none" : "medium",
            wing_tip_color: character.includes('WHITE_WING') ? "white" : "black",
            tail_color: character.includes('WHITE_TAIL') ? "white" :
                       character.includes('MIXED_TAIL') ? "mixed" : "black",
            face_pattern: character.includes('WEIRD_FACE') ? "weird_spots" :
                         character.includes('MOTTLED_FACE') ? "mottled" : "uniform_standard"
          }
        }
      });
    });

    return mockExamples;
  }

  private getCharacterDescription(character: string): string {
    const descriptions: Record<string, string> = {
      'BLUE_BAR': 'The classic city pigeon with blue-grey plumage and two black wing bars',
      'BLUE_CHECKER': 'A blue pigeon with checkered wing pattern',
      'MOTTLED_FACE': 'A pigeon with mottled facial coloring',
      'WEIRD_FACE_SPOTS': 'A pigeon with unusual facial markings',
      'BLUE_SPREAD_T_CHECK': 'A very dark pigeon with spread mutation and t-check pattern',
      'WHITE_PATCHES_PIEBALD': 'A pigeon with distinct white patches',
      'MOTTLED_PIEBALD': 'A pigeon with mottled white and colored feathers',
      'WHITE_TAIL': 'A pigeon with white tail feathers',
      'NO_IRIDESCENCE': 'A pigeon with no neck iridescence',
      'HIGH_IRIDESCENCE': 'A pigeon with brilliant neck iridescence',
      'MAGPIE': 'A striking black and white magpie',
      'WOOD_PIGEON': 'A large wood pigeon with distinctive white neck patches',
      'BABY_PIGEON': 'A young pigeon squab with developing plumage',
      'RARE_DISTINCTIVENESS': 'A pigeon with rare features like curly feathers',
      'BALDHEAD_PIGEON': 'A pigeon with entirely white head'
    };
    return descriptions[character] || 'Unknown pigeon character';
  }

  private generateDescription(character: string, distinctiveness: string): string {
    const baseDescriptions = {
      'common': 'This pigeon has typical coloration and patterns commonly seen in urban environments.',
      'unusual': 'This pigeon displays some distinctive features that make it stand out from typical feral pigeons.',
      'rare': 'This pigeon exhibits rare characteristics not commonly found in wild populations.'
    };

    return baseDescriptions[distinctiveness as keyof typeof baseDescriptions] || 'Standard pigeon appearance.';
  }

  /**
   * Get all classified pigeons
   */
  getClassifiedPigeons(): Observable<ClassifiedPigeon[]> {
    return this.classificationData$.asObservable();
  }

  /**
   * Get character distribution
   */
  getCharacterDistribution(): Observable<CharacterDistribution> {
    return this.characterDistribution$.asObservable();
  }

  /**
   * Get loading state
   */
  getLoadingState(): Observable<boolean> {
    return this.loading$.asObservable();
  }

  /**
   * Filter pigeons by character type
   */
  filterByCharacter(character: string): Observable<ClassifiedPigeon[]> {
    return this.classificationData$.pipe(
      map(pigeons => pigeons.filter(p => p.character === character))
    );
  }

  /**
   * Filter pigeons by distinctiveness
   */
  filterByDistinctiveness(distinctiveness: string): Observable<ClassifiedPigeon[]> {
    return this.classificationData$.pipe(
      map(pigeons => pigeons.filter(p => p.analysis.distinctiveness === distinctiveness))
    );
  }

  /**
   * Get unique character types
   */
  getUniqueCharacters(): Observable<string[]> {
    return this.classificationData$.pipe(
      map(pigeons => [...new Set(pigeons.map(p => p.character))].sort())
    );
  }

  /**
   * Get unique distinctiveness levels
   */
  getUniqueDistinctiveness(): Observable<string[]> {
    return this.classificationData$.pipe(
      map(pigeons => [...new Set(pigeons.map(p => p.analysis.distinctiveness))].sort())
    );
  }

  /**
   * Search pigeons by description or filename
   */
  searchPigeons(query: string): Observable<ClassifiedPigeon[]> {
    return this.classificationData$.pipe(
      map(pigeons => 
        pigeons.filter(p => 
          p.filename.toLowerCase().includes(query.toLowerCase()) ||
          p.description.toLowerCase().includes(query.toLowerCase()) ||
          p.analysis.description.toLowerCase().includes(query.toLowerCase())
        )
      )
    );
  }

  /**
   * Get character description from enum
   */
  getCharacterDisplayName(character: string): string {
    return character.replace(/_/g, ' ').toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Get character color based on type
   */
  getCharacterColor(character: string): string {
    if (character.includes('BLUE')) return 'blue';
    if (character.includes('BROWN')) return 'brown';
    if (character.includes('ASH_RED')) return 'red';
    if (character.includes('SPREAD')) return 'gray';
    if (character.includes('PIEBALD')) return 'purple';
    if (character.includes('MAGPIE') || character.includes('WOOD') || character.includes('DUCK')) return 'green';
    if (character.includes('DEAD') || character.includes('BABY')) return 'orange';
    return 'indigo';
  }
}
