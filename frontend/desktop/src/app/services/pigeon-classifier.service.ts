import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, map } from 'rxjs';

export interface PigeonTraits {
  base_color: "blue" | "ash-red" | "brown" | "other";
  main_pattern: "bar" | "checker" | "t-check" | "barless" | "other";
  is_spread: boolean;
  spread_level: "none" | "partial" | "full";
  is_piebald: boolean;
  piebald_level: "none" | "intermediate" | "heavy" | "full_white";
  piebald_pattern: "none" | "white_patches" | "small_white_spots" | "mottled";
  piebald_intensity: {
    head: "none" | "light" | "intermediate" | "heavy" | "fully_white";
    neck: "none" | "light" | "intermediate" | "heavy" | "fully_white";
    body: "none" | "light" | "intermediate" | "heavy" | "fully_white";
    tail: "none" | "light" | "intermediate" | "heavy" | "fully_white";
  } | null;
  head_pattern: "helmet" | "baldhead" | "none";
  iridescence_level: "none" | "low" | "medium" | "high";
  wing_tip_color: "black" | "dark" | "grey" | "light" | "white";
  tail_color: "black" | "dark" | "light" | "white" | "mixed";
  face_pattern: "uniform_standard" | "mottled" | "weird_spots";
}

export interface PigeonAnalysisResult {
  filename: string;
  looks_like_a_screenshot: number;
  is_bird: boolean;
  species: string;
  is_baby_pigeon: boolean;
  is_dead: boolean;
  distinctiveness: "common" | "unusual" | "rare";
  description: string;
  pigeon_traits: PigeonTraits | null;
}

export interface ClassifiedPigeon {
  filename: string;
  character: string;
  description: string;
  analysis: PigeonAnalysisResult;
  imageUrl: string;
}

export interface CharacterDistribution {
  [character: string]: number;
}

export interface ClassificationReport {
  total_images: number;
  total_unique_characters: number;
  target_characters: number;
  character_distribution: CharacterDistribution;
  examples: ClassifiedPigeon[];
}

@Injectable({
  providedIn: 'root'
})
export class PigeonClassifierService {
  private classificationData$ = new BehaviorSubject<ClassifiedPigeon[]>([]);
  private characterDistribution$ = new BehaviorSubject<CharacterDistribution>({});
  private loading$ = new BehaviorSubject<boolean>(false);

  constructor(private http: HttpClient) {}

  /**
   * Load classification data from the backend
   */
  loadClassificationData(): Observable<ClassificationReport> {
    this.loading$.next(true);

    // Load the real classification data from the index file
    return this.http.get<any>('assets/pigeon-images/index.json').pipe(
      map(indexData => {
        const report = this.processRealData(indexData);
        this.classificationData$.next(report.examples);
        this.characterDistribution$.next(report.character_distribution);
        this.loading$.next(false);
        return report;
      })
    );
  }

  /**
   * Process real classification data from the index file
   */
  private processRealData(indexData: any): ClassificationReport {
    const pigeons: ClassifiedPigeon[] = [];
    const characterDistribution: CharacterDistribution = {};

    indexData.characterGroups.forEach((group: any) => {
      characterDistribution[group.character] = group.count;

      group.pigeons.forEach((pigeonData: any) => {
        const pigeon: ClassifiedPigeon = {
          filename: pigeonData.filename,
          character: group.character,
          description: pigeonData.description,
          analysis: {
            filename: pigeonData.filename,
            looks_like_a_screenshot: 1,
            is_bird: pigeonData.species !== 'other',
            species: pigeonData.species,
            is_baby_pigeon: group.character === 'BABY_PIGEON',
            is_dead: group.character === 'DEAD_PIGEON',
            distinctiveness: pigeonData.distinctiveness as "common" | "unusual" | "rare",
            description: pigeonData.description,
            pigeon_traits: this.inferTraitsFromCharacter(group.character)
          },
          imageUrl: `assets/pigeon-images/${group.directory}/${pigeonData.filename}`
        };
        pigeons.push(pigeon);
      });
    });

    console.log(`Loaded ${pigeons.length} classified pigeons from real data`);

    return {
      total_images: indexData.totalImages,
      total_unique_characters: indexData.characters,
      target_characters: 50,
      character_distribution: characterDistribution,
      examples: pigeons
    };
  }

  /**
   * Infer pigeon traits from character name
   */
  private inferTraitsFromCharacter(character: string): PigeonTraits | null {
    // Return null for non-pigeon species
    if (character.includes('MAGPIE') || character.includes('WOOD') ||
        character.includes('DUCK') || character.includes('CROW') ||
        character.includes('RAVEN') || character.includes('SEAGULL') ||
        character.includes('ROOSTER') || character.includes('OTHER_BIRD') ||
        character.includes('TURTLEDOVE')) {
      return null;
    }

    // Basic trait inference from character name
    const traits: PigeonTraits = {
      base_color: "blue",
      main_pattern: "bar",
      is_spread: false,
      spread_level: "none",
      is_piebald: false,
      piebald_level: "none",
      piebald_pattern: "none",
      piebald_intensity: null,
      head_pattern: "none",
      iridescence_level: "medium",
      wing_tip_color: "black",
      tail_color: "black",
      face_pattern: "uniform_standard"
    };

    // Infer base color
    if (character.includes('BLUE')) {
      traits.base_color = 'blue';
    } else if (character.includes('BROWN')) {
      traits.base_color = 'brown';
    } else if (character.includes('ASH_RED')) {
      traits.base_color = 'ash-red';
    } else {
      traits.base_color = 'other';
    }

    // Infer pattern
    if (character.includes('BAR')) {
      traits.main_pattern = 'bar';
    } else if (character.includes('CHECKER')) {
      traits.main_pattern = 'checker';
    } else if (character.includes('T_CHECK')) {
      traits.main_pattern = 't-check';
    } else if (character.includes('BARLESS')) {
      traits.main_pattern = 'barless';
    } else {
      traits.main_pattern = 'other';
    }

    // Infer piebald
    traits.is_piebald = character.includes('PIEBALD');
    if (traits.is_piebald) {
      if (character.includes('HEAVY')) {
        traits.piebald_level = 'heavy';
      } else if (character.includes('LIGHT')) {
        traits.piebald_level = 'intermediate';
      } else {
        traits.piebald_level = 'intermediate';
      }

      if (character.includes('MOTTLED')) {
        traits.piebald_pattern = 'mottled';
      } else if (character.includes('WHITE_PATCHES')) {
        traits.piebald_pattern = 'white_patches';
      } else if (character.includes('SMALL_WHITE_SPOTS')) {
        traits.piebald_pattern = 'small_white_spots';
      }

      traits.piebald_intensity = {
        head: "light",
        neck: "none",
        body: "intermediate",
        tail: "light"
      };
    }

    // Infer spread
    if (character.includes('SPREAD')) {
      traits.is_spread = true;
      traits.spread_level = 'full';
    }

    // Infer head pattern
    if (character.includes('HELMET')) {
      traits.head_pattern = 'helmet';
    } else if (character.includes('BALDHEAD')) {
      traits.head_pattern = 'baldhead';
    }

    // Infer face pattern
    if (character.includes('MOTTLED_FACE')) {
      traits.face_pattern = 'mottled';
    } else if (character.includes('WEIRD_FACE')) {
      traits.face_pattern = 'weird_spots';
    }

    // Infer iridescence
    if (character.includes('HIGH_IRIDESCENCE')) {
      traits.iridescence_level = 'high';
    } else if (character.includes('NO_IRIDESCENCE')) {
      traits.iridescence_level = 'none';
    }

    // Infer wing/tail colors
    if (character.includes('WHITE_WING')) {
      traits.wing_tip_color = 'white';
    }
    if (character.includes('WHITE_TAIL')) {
      traits.tail_color = 'white';
    } else if (character.includes('MIXED_TAIL')) {
      traits.tail_color = 'mixed';
    }

    return traits;
  }



  /**
   * Get all classified pigeons
   */
  getClassifiedPigeons(): Observable<ClassifiedPigeon[]> {
    return this.classificationData$.asObservable();
  }

  /**
   * Get character distribution
   */
  getCharacterDistribution(): Observable<CharacterDistribution> {
    return this.characterDistribution$.asObservable();
  }

  /**
   * Get loading state
   */
  getLoadingState(): Observable<boolean> {
    return this.loading$.asObservable();
  }

  /**
   * Filter pigeons by character type
   */
  filterByCharacter(character: string): Observable<ClassifiedPigeon[]> {
    return this.classificationData$.pipe(
      map(pigeons => pigeons.filter(p => p.character === character))
    );
  }

  /**
   * Filter pigeons by distinctiveness
   */
  filterByDistinctiveness(distinctiveness: string): Observable<ClassifiedPigeon[]> {
    return this.classificationData$.pipe(
      map(pigeons => pigeons.filter(p => p.analysis.distinctiveness === distinctiveness))
    );
  }

  /**
   * Get unique character types
   */
  getUniqueCharacters(): Observable<string[]> {
    return this.classificationData$.pipe(
      map(pigeons => [...new Set(pigeons.map(p => p.character))].sort())
    );
  }

  /**
   * Get unique distinctiveness levels
   */
  getUniqueDistinctiveness(): Observable<string[]> {
    return this.classificationData$.pipe(
      map(pigeons => [...new Set(pigeons.map(p => p.analysis.distinctiveness))].sort())
    );
  }

  /**
   * Search pigeons by description or filename
   */
  searchPigeons(query: string): Observable<ClassifiedPigeon[]> {
    return this.classificationData$.pipe(
      map(pigeons => 
        pigeons.filter(p => 
          p.filename.toLowerCase().includes(query.toLowerCase()) ||
          p.description.toLowerCase().includes(query.toLowerCase()) ||
          p.analysis.description.toLowerCase().includes(query.toLowerCase())
        )
      )
    );
  }

  /**
   * Get character description from enum
   */
  getCharacterDisplayName(character: string): string {
    return character.replace(/_/g, ' ').toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Get character color based on type
   */
  getCharacterColor(character: string): string {
    if (character.includes('BLUE')) return 'blue';
    if (character.includes('BROWN')) return 'brown';
    if (character.includes('ASH_RED')) return 'red';
    if (character.includes('SPREAD')) return 'gray';
    if (character.includes('PIEBALD')) return 'purple';
    if (character.includes('MAGPIE') || character.includes('WOOD') || character.includes('DUCK')) return 'green';
    if (character.includes('DEAD') || character.includes('BABY')) return 'orange';
    return 'indigo';
  }
}
