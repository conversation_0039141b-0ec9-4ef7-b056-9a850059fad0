import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { AnalysisProgress } from '../../services/analysis-polling.service';

@Component({
  selector: 'app-analysis-progress',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatProgressBarModule,
    MatIconModule,
    MatButtonModule
  ],
  template: `
    @if (progress) {
      <div class="progress-container">
        <mat-card class="progress-card">
          <mat-card-header>
            <div mat-card-avatar [class]="getAvatarClass()">
              <mat-icon>{{ getStatusIcon() }}</mat-icon>
            </div>
            <mat-card-title>{{ getTitle() }}</mat-card-title>
            <mat-card-subtitle>{{ progress.message }}</mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            @if (progress.status === 'PENDING') {
              <div class="progress-section">
                <mat-progress-bar 
                  mode="determinate" 
                  [value]="progress.progress">
                </mat-progress-bar>
                <div class="progress-text">
                  {{ progress.progress.toFixed(0) }}% Complete
                </div>
              </div>
              
              <div class="analysis-steps">
                <div class="step" [class.active]="progress.progress >= 10">
                  <mat-icon>cloud_upload</mat-icon>
                  <span>Image Uploaded</span>
                </div>
                <div class="step" [class.active]="progress.progress >= 30">
                  <mat-icon>psychology</mat-icon>
                  <span>AI Analysis</span>
                </div>
                <div class="step" [class.active]="progress.progress >= 60">
                  <mat-icon>compare</mat-icon>
                  <span>Matching Pigeons</span>
                </div>
                <div class="step" [class.active]="progress.progress >= 90">
                  <mat-icon>location_on</mat-icon>
                  <span>Gang Assignment</span>
                </div>
              </div>
            }

            @if (progress.status === 'FINISHED') {
              <div class="success-section">
                <mat-icon class="success-icon">check_circle</mat-icon>
                <p>Your pigeon has been successfully analyzed and added to your collection!</p>
              </div>
            }

            @if (progress.status === 'ERROR') {
              <div class="error-section">
                <mat-icon class="error-icon">error</mat-icon>
                <p>{{ progress.message }}</p>
                @if (progress.error) {
                  <div class="error-details">
                    <strong>Error Code:</strong> {{ progress.error }}
                  </div>
                }
              </div>
            }

            @if (progress.job) {
              <div class="job-details">
                <h4>Analysis Details</h4>
                <div class="detail-row">
                  <strong>Capture ID:</strong>
                  <code>{{ progress.job.captureId }}</code>
                </div>
                <div class="detail-row">
                  <strong>Started:</strong>
                  {{ formatDate(progress.job.createdAt) }}
                </div>
                @if (progress.job.updatedAt) {
                  <div class="detail-row">
                    <strong>Last Updated:</strong>
                    {{ formatDate(progress.job.updatedAt) }}
                  </div>
                }
              </div>
            }
          </mat-card-content>

          @if (progress.status === 'ERROR') {
            <mat-card-actions>
              <button mat-button color="primary">
                <mat-icon>refresh</mat-icon>
                Try Again
              </button>
            </mat-card-actions>
          }
        </mat-card>
      </div>
    }
  `,
  styles: [`
    .progress-container {
      max-width: 600px;
      margin: 20px auto;
      padding: 0 16px;
    }

    .progress-card {
      width: 100%;
    }

    .progress-section {
      margin: 16px 0;
    }

    .progress-text {
      text-align: center;
      margin-top: 8px;
      font-weight: 500;
      color: #666;
    }

    .analysis-steps {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 16px;
      margin-top: 24px;
    }

    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 16px;
      border-radius: 8px;
      background-color: #f5f5f5;
      transition: all 0.3s ease;
      opacity: 0.5;
    }

    .step.active {
      background-color: #e3f2fd;
      opacity: 1;
      transform: scale(1.05);
    }

    .step mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
      color: #666;
    }

    .step.active mat-icon {
      color: #2196f3;
    }

    .step span {
      font-size: 12px;
      text-align: center;
      font-weight: 500;
    }

    .success-section {
      text-align: center;
      padding: 24px;
    }

    .success-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #4caf50;
      margin-bottom: 16px;
    }

    .error-section {
      text-align: center;
      padding: 24px;
    }

    .error-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #f44336;
      margin-bottom: 16px;
    }

    .error-details {
      margin-top: 16px;
      padding: 12px;
      background-color: #ffebee;
      border-radius: 4px;
      font-family: 'Roboto Mono', monospace;
      font-size: 14px;
    }

    .job-details {
      margin-top: 24px;
      padding: 16px;
      background-color: #f9f9f9;
      border-radius: 8px;
    }

    .job-details h4 {
      margin: 0 0 12px 0;
      color: #333;
    }

    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      gap: 16px;
    }

    .detail-row code {
      background-color: #e0e0e0;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: 'Roboto Mono', monospace;
      font-size: 12px;
      word-break: break-all;
      flex-shrink: 0;
    }

    /* Avatar styling based on status */
    .avatar-pending {
      background-color: #ff9800;
      color: white;
    }

    .avatar-finished {
      background-color: #4caf50;
      color: white;
    }

    .avatar-error {
      background-color: #f44336;
      color: white;
    }

    @media (max-width: 600px) {
      .analysis-steps {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
      }
      
      .step {
        padding: 12px;
      }
      
      .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }
    }
  `]
})
export class AnalysisProgressComponent {
  @Input() progress: AnalysisProgress | null = null;

  getTitle(): string {
    if (!this.progress) return '';
    
    switch (this.progress.status) {
      case 'PENDING':
        return 'Analyzing Your Pigeon';
      case 'FINISHED':
        return 'Analysis Complete!';
      case 'ERROR':
        return 'Analysis Failed';
      default:
        return 'Processing...';
    }
  }

  getStatusIcon(): string {
    if (!this.progress) return 'help';
    
    switch (this.progress.status) {
      case 'PENDING':
        return 'hourglass_empty';
      case 'FINISHED':
        return 'check_circle';
      case 'ERROR':
        return 'error';
      default:
        return 'help';
    }
  }

  getAvatarClass(): string {
    if (!this.progress) return '';
    
    switch (this.progress.status) {
      case 'PENDING':
        return 'avatar-pending';
      case 'FINISHED':
        return 'avatar-finished';
      case 'ERROR':
        return 'avatar-error';
      default:
        return '';
    }
  }

  formatDate(date: Date | string): string {
    if (!date) return '';
    
    const dateObj = date instanceof Date ? date : new Date(date);
    return dateObj.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}
