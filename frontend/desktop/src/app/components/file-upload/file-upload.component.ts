import { Component, EventEmitter, Output, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { FirebaseService } from '../../services/firebase.service';

export interface UploadResult {
  captureId: string;
  storageFilePath: string;
  downloadUrl: string;
}

@Component({
  selector: 'app-file-upload',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatSnackBarModule,
  ],
  template: `
    <div class="upload-container">
      <div
        class="drop-zone"
        [class.drag-over]="isDragOver()"
        [class.uploading]="isUploading()"
        (dragover)="onDragOver($event)"
        (dragleave)="onDragLeave($event)"
        (drop)="onDrop($event)"
        (click)="fileInput.click()"
      >
        <input
          #fileInput
          type="file"
          accept="image/*"
          (change)="onFileSelected($event)"
          style="display: none;"
        />

        <div class="drop-zone-content">
          @if (isUploading()) {
          <mat-icon class="upload-icon">cloud_upload</mat-icon>
          <h3>Uploading...</h3>
          <mat-progress-bar mode="indeterminate"></mat-progress-bar>
          } @else {
          <mat-icon class="upload-icon">add_photo_alternate</mat-icon>
          <h3>Drop your pigeon photo here</h3>
          <p>or click to select a file</p>
          <button mat-raised-button color="primary">
            <mat-icon>upload</mat-icon>
            Choose File
          </button>
          }
        </div>
      </div>

      @if (selectedFile()) {
      <div class="file-info">
        <mat-icon>image</mat-icon>
        <span>{{ selectedFile()?.name }}</span>
        <button
          mat-icon-button
          (click)="clearFile()"
          [disabled]="isUploading()"
        >
          <mat-icon>close</mat-icon>
        </button>
      </div>
      } @if (selectedFile() && !isUploading()) {
      <div class="location-section">
        <h4>Location Information</h4>
        <p>We'll use your current location for the pigeon capture.</p>
        <button
          mat-raised-button
          color="accent"
          (click)="uploadFile()"
          [disabled]="!selectedFile()"
        >
          <mat-icon>send</mat-icon>
          Upload & Analyze Pigeon
        </button>
      </div>
      }
    </div>
  `,
  styles: [
    `
      .upload-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }

      .drop-zone {
        border: 2px dashed #ccc;
        border-radius: 12px;
        padding: 40px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: #fafafa;
      }

      .drop-zone:hover {
        border-color: #2196f3;
        background-color: #f0f8ff;
      }

      .drop-zone.drag-over {
        border-color: #4caf50;
        background-color: #f1f8e9;
      }

      .drop-zone.uploading {
        border-color: #ff9800;
        background-color: #fff8e1;
        cursor: not-allowed;
      }

      .drop-zone-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
      }

      .upload-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #666;
      }

      .file-info {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-top: 16px;
        padding: 12px;
        background-color: #e3f2fd;
        border-radius: 8px;
      }

      .location-section {
        margin-top: 20px;
        padding: 16px;
        background-color: #f5f5f5;
        border-radius: 8px;
        text-align: center;
      }

      .location-section h4 {
        margin: 0 0 8px 0;
        color: #333;
      }

      .location-section p {
        margin: 0 0 16px 0;
        color: #666;
      }

      mat-progress-bar {
        width: 100%;
        margin-top: 16px;
      }
    `,
  ],
})
export class FileUploadComponent {
  private firebaseService = inject(FirebaseService);
  private snackBar = inject(MatSnackBar);

  @Output() uploadComplete = new EventEmitter<UploadResult>();

  selectedFile = signal<File | null>(null);
  isDragOver = signal(false);
  isUploading = signal(false);

  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver.set(true);
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver.set(false);
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver.set(false);

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFile(files[0]);
    }
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.handleFile(input.files[0]);
    }
  }

  private handleFile(file: File) {
    if (!file.type.startsWith('image/')) {
      this.snackBar.open('Please select an image file', 'Close', {
        duration: 3000,
      });
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      // 10MB limit
      this.snackBar.open('File size must be less than 10MB', 'Close', {
        duration: 3000,
      });
      return;
    }

    this.selectedFile.set(file);
  }

  clearFile() {
    this.selectedFile.set(null);
  }

  async uploadFile() {
    const file = this.selectedFile();
    if (!file) return;

    try {
      this.isUploading.set(true);

      // Get user's current location
      const position = await this.getCurrentPosition();
      const latitude = position.coords.latitude;
      const longitude = position.coords.longitude;

      // Upload file to Firebase Storage
      const result = await this.firebaseService.uploadPigeonImage(
        file,
        latitude,
        longitude
      );

      this.snackBar.open(
        'File uploaded successfully! Analysis started.',
        'Close',
        { duration: 3000 }
      );
      this.uploadComplete.emit(result);

      // Reset component state
      this.selectedFile.set(null);
    } catch (error) {
      console.error('Upload error:', error);
      this.snackBar.open('Upload failed. Please try again.', 'Close', {
        duration: 5000,
      });
    } finally {
      this.isUploading.set(false);
    }
  }

  private getCurrentPosition(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        resolve,
        (error) => {
          // If geolocation fails, use default coordinates (NYC)
          console.warn('Geolocation failed, using default coordinates:', error);
          resolve({
            coords: {
              latitude: 40.7128,
              longitude: -74.006,
              accuracy: 0,
              altitude: null,
              altitudeAccuracy: null,
              heading: null,
              speed: null,
            },
            timestamp: Date.now(),
          } as GeolocationPosition);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000, // 5 minutes
        }
      );
    });
  }
}
