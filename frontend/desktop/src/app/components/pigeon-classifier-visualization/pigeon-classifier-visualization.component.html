<div class="pigeon-classifier-container p-6 max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">
      Pigeon Character Classifier Visualization
    </h1>
    <p class="text-gray-600">
      Explore how the AI classifier categorizes different pigeon phenotypes into game characters
    </p>
  </div>

  <!-- Stats Cards -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
    <mat-card class="stats-card">
      <mat-card-content class="text-center">
        <div class="text-2xl font-bold text-blue-600">{{ totalPigeons() }}</div>
        <div class="text-sm text-gray-600">Total Pigeons</div>
      </mat-card-content>
    </mat-card>
    
    <mat-card class="stats-card">
      <mat-card-content class="text-center">
        <div class="text-2xl font-bold text-green-600">{{ totalCharacters() }}</div>
        <div class="text-sm text-gray-600">Unique Characters</div>
      </mat-card-content>
    </mat-card>
    
    <mat-card class="stats-card">
      <mat-card-content class="text-center">
        <div class="text-2xl font-bold text-purple-600">{{ filteredCount() }}</div>
        <div class="text-sm text-gray-600">Filtered Results</div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Filters -->
  <mat-card class="mb-6">
    <mat-card-header>
      <mat-card-title>Filters & Search</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        <!-- Search -->
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Search</mat-label>
          <input matInput
                 [value]="searchQuery()"
                 (input)="onSearchQueryChange($any($event.target).value || '')"
                 placeholder="Search by filename, description...">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <!-- Character Filter -->
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Character Type</mat-label>
          <mat-select [value]="selectedCharacter()" 
                      (selectionChange)="onCharacterFilterChange($event.value)">
            <mat-option value="">All Characters</mat-option>
            <mat-option *ngFor="let character of uniqueCharacters()" [value]="character">
              {{ getCharacterDisplayName(character) }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Distinctiveness Filter -->
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Distinctiveness</mat-label>
          <mat-select [value]="selectedDistinctiveness()" 
                      (selectionChange)="onDistinctivenessFilterChange($event.value)">
            <mat-option value="">All Levels</mat-option>
            <mat-option *ngFor="let level of uniqueDistinctiveness()" [value]="level">
              {{ level | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- Sort By -->
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Sort By</mat-label>
          <mat-select [value]="sortBy()" 
                      (selectionChange)="onSortChange($event.value)">
            <mat-option value="character">Character Type</mat-option>
            <mat-option value="distinctiveness">Distinctiveness</mat-option>
            <mat-option value="filename">Filename</mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="flex justify-between items-center">
        <button mat-raised-button color="warn" (click)="clearFilters()">
          <mat-icon>clear</mat-icon>
          Clear Filters
        </button>
        
        <div class="text-sm text-gray-600">
          Showing {{ filteredCount() }} of {{ totalPigeons() }} pigeons
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Loading Spinner -->
  <div *ngIf="loading()" class="flex justify-center items-center py-12">
    <mat-spinner diameter="50"></mat-spinner>
  </div>

  <!-- Pigeon Cards Grid -->
  <div *ngIf="!loading()" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    <mat-card *ngFor="let pigeon of filteredPigeons(); trackBy: trackByFilename" 
              class="pigeon-card hover:shadow-lg transition-shadow duration-300">
      
      <!-- Image -->
      <div class="relative">
        <img [src]="pigeon.imageUrl"
             [alt]="pigeon.filename"
             class="w-full h-48 object-cover rounded-t-lg"
             (error)="$any($event.target).src = 'assets/placeholder-pigeon.svg'">
        
        <!-- Character Badge -->
        <div class="absolute top-2 right-2">
          <mat-chip-set>
            <mat-chip [style.background-color]="getCharacterColor(pigeon.character)"
                      class="text-white font-semibold">
              {{ getCharacterDisplayName(pigeon.character) }}
            </mat-chip>
          </mat-chip-set>
        </div>

        <!-- Distinctiveness Badge -->
        <div class="absolute top-2 left-2">
          <mat-chip-set>
            <mat-chip [style.background-color]="getDistinctivenessColor(pigeon.analysis.distinctiveness)"
                      class="text-white text-xs">
              {{ pigeon.analysis.distinctiveness | titlecase }}
            </mat-chip>
          </mat-chip-set>
        </div>
      </div>

      <mat-card-content class="p-4">
        <!-- Filename -->
        <h3 class="font-semibold text-lg mb-2 truncate" [title]="pigeon.filename">
          {{ pigeon.filename }}
        </h3>

        <!-- Character Description -->
        <p class="text-sm text-gray-600 mb-3">
          {{ pigeon.description }}
        </p>

        <!-- Analysis Description -->
        <div class="bg-gray-50 p-3 rounded-lg mb-3">
          <p class="text-xs text-gray-700 leading-relaxed">
            {{ pigeon.analysis.description }}
          </p>
        </div>

        <!-- Traits Summary -->
        <div *ngIf="pigeon.analysis.pigeon_traits" class="space-y-2">
          <div class="flex flex-wrap gap-1">
            <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
              {{ pigeon.analysis.pigeon_traits.base_color | titlecase }}
            </span>
            <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
              {{ pigeon.analysis.pigeon_traits.main_pattern | titlecase }}
            </span>
            <span *ngIf="pigeon.analysis.pigeon_traits.is_piebald" 
                  class="inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded">
              Piebald
            </span>
            <span *ngIf="pigeon.analysis.pigeon_traits.is_spread" 
                  class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
              Spread
            </span>
          </div>
        </div>

        <!-- Species Info -->
        <div class="mt-3 pt-3 border-t border-gray-200">
          <div class="flex justify-between items-center text-xs text-gray-500">
            <span>{{ pigeon.analysis.species | titlecase }}</span>
            <span *ngIf="pigeon.analysis.is_baby_pigeon" class="text-orange-600">Baby</span>
            <span *ngIf="pigeon.analysis.is_dead" class="text-red-600">Deceased</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- No Results -->
  <div *ngIf="!loading() && filteredCount() === 0" 
       class="text-center py-12">
    <mat-icon class="text-6xl text-gray-400 mb-4">search_off</mat-icon>
    <h3 class="text-xl font-semibold text-gray-600 mb-2">No pigeons found</h3>
    <p class="text-gray-500 mb-4">Try adjusting your filters or search terms</p>
    <button mat-raised-button color="primary" (click)="clearFilters()">
      Clear All Filters
    </button>
  </div>
</div>
