import { Component, OnInit, OnD<PERSON>roy, ChangeDetectionStrategy, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Subject, takeUntil, combineLatest, debounceTime, distinctUntilChanged, of } from 'rxjs';

import { PigeonClassifierService, ClassifiedPigeon, CharacterDistribution } from '../../services/pigeon-classifier.service';

@Component({
  selector: 'app-pigeon-classifier-visualization',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatChipsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatBadgeModule,
    MatTooltipModule
  ],
  templateUrl: './pigeon-classifier-visualization.component.html',
  styleUrls: ['./pigeon-classifier-visualization.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PigeonClassifierVisualizationComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Signals for reactive state management
  allPigeons = signal<ClassifiedPigeon[]>([]);
  filteredPigeons = signal<ClassifiedPigeon[]>([]);
  characterDistribution = signal<CharacterDistribution>({});
  uniqueCharacters = signal<string[]>([]);
  uniqueDistinctiveness = signal<string[]>([]);
  loading = signal<boolean>(false);

  // Filter controls
  selectedCharacter = signal<string>('');
  selectedDistinctiveness = signal<string>('');
  searchQuery = signal<string>('');
  sortBy = signal<'character' | 'distinctiveness' | 'filename'>('character');

  // Computed values
  totalPigeons = computed(() => this.allPigeons().length);
  totalCharacters = computed(() => this.uniqueCharacters().length);
  filteredCount = computed(() => this.filteredPigeons().length);

  constructor(private pigeonClassifierService: PigeonClassifierService) {}

  ngOnInit(): void {
    this.loadData();
    this.setupFiltering();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadData(): void {
    this.loading.set(true);

    // Load initial data first
    this.pigeonClassifierService.loadClassificationData().pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      // Then load all the individual data streams
      this.pigeonClassifierService.getClassifiedPigeons().pipe(
        takeUntil(this.destroy$)
      ).subscribe(pigeons => {
        this.allPigeons.set(pigeons);
        this.applyFilters();
      });

      this.pigeonClassifierService.getCharacterDistribution().pipe(
        takeUntil(this.destroy$)
      ).subscribe(distribution => {
        this.characterDistribution.set(distribution);
      });

      this.pigeonClassifierService.getUniqueCharacters().pipe(
        takeUntil(this.destroy$)
      ).subscribe(characters => {
        this.uniqueCharacters.set(characters);
      });

      this.pigeonClassifierService.getUniqueDistinctiveness().pipe(
        takeUntil(this.destroy$)
      ).subscribe(distinctiveness => {
        this.uniqueDistinctiveness.set(distinctiveness);
      });

      this.loading.set(false);
    });
  }

  private setupFiltering(): void {
    // React to filter changes using effect or manual subscription
    // For now, we'll handle filtering in the methods directly
    // since signals don't have asObservable() method
  }

  private applyFilters(): void {
    let filtered = [...this.allPigeons()];

    // Apply character filter
    if (this.selectedCharacter()) {
      filtered = filtered.filter(p => p.character === this.selectedCharacter());
    }

    // Apply distinctiveness filter
    if (this.selectedDistinctiveness()) {
      filtered = filtered.filter(p => p.analysis.distinctiveness === this.selectedDistinctiveness());
    }

    // Apply search filter
    if (this.searchQuery()) {
      const query = this.searchQuery().toLowerCase();
      filtered = filtered.filter(p =>
        p.filename.toLowerCase().includes(query) ||
        p.description.toLowerCase().includes(query) ||
        p.analysis.description.toLowerCase().includes(query) ||
        p.character.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (this.sortBy()) {
        case 'character':
          return a.character.localeCompare(b.character);
        case 'distinctiveness':
          return a.analysis.distinctiveness.localeCompare(b.analysis.distinctiveness);
        case 'filename':
          return a.filename.localeCompare(b.filename);
        default:
          return 0;
      }
    });

    this.filteredPigeons.set(filtered);
  }

  onCharacterFilterChange(character: string): void {
    this.selectedCharacter.set(character);
    this.applyFilters();
  }

  onDistinctivenessFilterChange(distinctiveness: string): void {
    this.selectedDistinctiveness.set(distinctiveness);
    this.applyFilters();
  }

  onSearchQueryChange(query: string): void {
    this.searchQuery.set(query);
    this.applyFilters();
  }

  onSortChange(sortBy: 'character' | 'distinctiveness' | 'filename'): void {
    this.sortBy.set(sortBy);
    this.applyFilters();
  }

  clearFilters(): void {
    this.selectedCharacter.set('');
    this.selectedDistinctiveness.set('');
    this.searchQuery.set('');
    this.applyFilters();
  }

  getCharacterDisplayName(character: string): string {
    return this.pigeonClassifierService.getCharacterDisplayName(character);
  }

  getCharacterColor(character: string): string {
    return this.pigeonClassifierService.getCharacterColor(character);
  }

  getDistinctivenessColor(distinctiveness: string): string {
    switch (distinctiveness) {
      case 'common': return 'green';
      case 'unusual': return 'orange';
      case 'rare': return 'red';
      default: return 'gray';
    }
  }

  trackByFilename(index: number, pigeon: ClassifiedPigeon): string {
    return pigeon.filename;
  }
}
