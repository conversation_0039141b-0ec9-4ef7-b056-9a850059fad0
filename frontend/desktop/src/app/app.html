<!-- Pigeon Analyzer Application -->
<div class="app-container">
  <!-- Header Toolbar -->
  <mat-toolbar color="primary" class="app-toolbar">
    <mat-icon class="app-icon">pets</mat-icon>
    <span class="app-title">{{ title }}</span>
    <span class="spacer"></span>

    <!-- Navigation buttons -->
    <button mat-icon-button
            routerLink="/"
            routerLinkActive="active-nav-button"
            [routerLinkActiveOptions]="{exact: true}"
            matTooltip="Capture Pigeon">
      <mat-icon>camera_alt</mat-icon>
    </button>
    <button mat-icon-button
            routerLink="/deck"
            routerLinkActive="active-nav-button"
            matTooltip="My Pigeon Deck">
      <mat-icon>collections</mat-icon>
    </button>
  </mat-toolbar>

  <!-- Main Content Area -->
  <main class="main-content">
    <router-outlet></router-outlet>
  </main>
</div>


