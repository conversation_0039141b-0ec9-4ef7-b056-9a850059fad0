.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.app-toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.app-icon {
  margin-right: 8px;
}

.app-title {
  font-size: 1.2rem;
  font-weight: 500;
}

.spacer {
  flex: 1 1 auto;
}

.active-nav-button {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

// Responsive design
@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }
}