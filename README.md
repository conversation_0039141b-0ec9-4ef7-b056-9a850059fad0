# Pigeon Go

A location-based mobile game where players capture and battle with pigeons found in the real world.

## Project Overview

Pigeon Go is a mobile game that allows players to:
- Capture pigeons in the real world using their phone's camera and GPS
- Train and level up their pigeons
- Battle other players' pigeons
- Collect items to equip their pigeons
- Join pigeon gangs based on geographic location

## Game Mechanics

### Pigeon Gangs

Pigeons belong to one of five gangs based on the geographic area where they were captured:

- **Gang One** through **Gang Five** are distributed in a grid system
- Gang Five is rarer and moves randomly
- Gang positions rotate hourly
- The system uses a 192-hour (8-day) cycle starting January 1, 2025 at 0 AM
- Gang Five's position is determined by a 64-entry cycle array

## Project Structure

The project is divided into two main parts:

- **Backend**: Firebase Functions (Node.js/TypeScript) for game logic and data management
- **Frontend**: React Native mobile application (planned)

## Technologies Used

### Backend
- Firebase (Firestore, Cloud Functions, Storage)
- Node.js
- TypeScript
- OpenAI API for pigeon analysis

### Frontend (Planned)
- React Native
- TypeScript
- Firebase SDK

## Getting Started

### Prerequisites

- Node.js (v22)
- Firebase CLI
- React Native development environment (for frontend)

### Backend Setup

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Install dependencies:
   ```
   cd functions
   npm install
   ```

3. Create a `.env` file in the `functions` directory with your OpenAI API key:
   ```
   OPENAI_API_KEY=your_api_key_here
   ```

4. Create a `firebaseServiceAccount.ts` file in the `functions/src` directory with your Firebase service account credentials.

5. Run the Firebase emulator:
   ```
   npm run serve
   ```

### Frontend Setup (Coming Soon)

Instructions for setting up the React Native frontend will be added once development begins.

## Testing

### Backend Tests

Run the backend tests:
```
cd backend/functions
npm test
```

## Deployment

### Backend Deployment

Deploy the Firebase functions:
```
cd backend/functions
npm run deploy
```

## License

[Add license information here]

## Contributors

[Add contributor information here]
