const path = require("path");

module.exports = {
    root: true,
    env: {
        es6: true,
        node: true,
    },
    extends: [
        "eslint:recommended",
        "plugin:import/errors",
        "plugin:import/warnings",
        "plugin:import/typescript",
        "plugin:@typescript-eslint/recommended",
        "plugin:prettier/recommended",
    ],
    parser: "@typescript-eslint/parser",
    parserOptions: {
        project: [path.resolve(__dirname, "tsconfig.json")],
        sourceType: "module",
    },
    ignorePatterns: [
        "/lib/**/*", // Ignore built files.
        "/generated/**/*", // Ignore generated files.
        ".eslintrc.js",
        "jest.config.js",
        "src/scripts/**/*",
    ],
    plugins: ["@typescript-eslint", "import", "prettier"],
    rules: {
        "import/no-unresolved": 0,
        "prettier/prettier": "error",
    },
};
