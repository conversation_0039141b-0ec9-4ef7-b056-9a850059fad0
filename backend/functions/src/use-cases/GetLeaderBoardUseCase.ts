import { Trainer } from "../domain/entities/Trainer";
import { TrainerStats, TrainerStatsData } from "../domain/entities/TrainerStats";
import { TrainerStatsOrderBy } from "../domain/enums/TrainerStatsOrderBy";
import { TrainerRepository } from "../domain/repositories/TrainerRepository";
import { TrainerStatsRepository } from "../domain/repositories/TrainerStatsRepository";

interface TrainerStatsWithTrainerInfo extends TrainerStatsData {
    trainerUsername: string;
}

export class GetLeaderBoardUseCase {
    constructor(
        private trainerStatsRepo: TrainerStatsRepository,
        private trainerRepo: TrainerRepository,
    ) {}

    async execute(
        orderBy: TrainerStatsOrderBy = TrainerStatsOrderBy.TOTAL_CATCHER_POINTS,
        limit = 100,
    ): Promise<{ leaderBoard: TrainerStatsWithTrainerInfo[] }> {
        const leaderBoard = await this.trainerStatsRepo.getLeaderBoard(orderBy, limit);
        const leaderBoardUserIds = leaderBoard.map((s) => s.trainerId);
        const trainers = await this.trainerRepo.getByIds(leaderBoardUserIds);
        const trainersById = trainers.reduce(
            (acc, t) => {
                acc[t.id] = t;
                return acc;
            },
            {} as { [id: string]: Trainer },
        );

        return { leaderBoard: leaderBoard.map((s) => ({ ...s, trainerUsername: trainersById[s.trainerId].username })) };
    }
}
