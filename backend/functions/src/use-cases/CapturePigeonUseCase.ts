import { v4 as uuidv4 } from "uuid";
import { DEFAULT_MAX_FATIGUE, Pigeon } from "../domain/entities/Pigeon";
import { AnalysisJobErrorCode } from "../domain/enums/AnalysisJobErrorCode";
import { PigeonRarity } from "../domain/enums/PigeonRarity";
import { AnalysisJobRepository } from "../domain/repositories/AnalysisJobRepository";
import { PigeonRepository } from "../domain/repositories/PigeonRepository";
import { TrainerRepository } from "../domain/repositories/TrainerRepository";
import { AnalysisService } from "../domain/services/AnalysisService";
import { Coordinates, GeoLocationService } from "../domain/services/GeoLocationService";
import { StatsService } from "../domain/services/StatsService";
import { PingExternalServiceUseCase } from "./PingExternalServiceUseCase";
import { Trainer } from "../domain/entities/Trainer";
import { TrainerStatsRepository } from "../domain/repositories/TrainerStatsRepository";
import { TrainerStats } from "../domain/entities/TrainerStats";
import { BasePigeonStatsRepository } from "../domain/repositories/BasePigeonStatsRepository";
import { BasePigeonStats } from "../domain/entities/BasePigeonStats";

export class CapturePigeonUseCase {
    constructor(
        private readonly trainerRepo: TrainerRepository,
        private readonly pigeonRepo: PigeonRepository,
        private readonly analysisService: AnalysisService,
        private readonly geoLocationService: GeoLocationService,
        private readonly analysisJobRepo: AnalysisJobRepository,
        private readonly pingExternalServiceUseCase: PingExternalServiceUseCase,
        private readonly statsService: StatsService,
        private readonly trainerStatsRepo: TrainerStatsRepository,
        private readonly basePigeonStatsRepo: BasePigeonStatsRepository,
    ) {}

    async execute(storageFilePath: string, imageUrl: string): Promise<void> {
        // storageFilePath is like "shots/<trainerId>/<captureId>/<latitude>_<longitude>_<fileName>.jpg"
        // We need to extract the trainerId and coordinates from the path

        console.log("Started at : ", new Date());
        const pathParts = storageFilePath.split("/");
        const captureDate = new Date();
        if (pathParts.length < 3) {
            throw new Error("Invalid file path format");
        }

        const trainerId = pathParts[1];
        const captureId = pathParts[2];

        // Get the analysis job - it should already exist
        const analysisJob = await this.analysisJobRepo.getByCaptureId(captureId);
        if (!analysisJob) {
            console.error(`No analysis job found for captureId: ${captureId}`);
            return;
        }

        try {
            let trainer = await this.trainerRepo.getById(trainerId);
            if (!trainer) {
                // create a trainer
                const newTrainer = new Trainer({
                    id: trainerId,
                    username: trainerId,
                    pigeonBalls: 1000,
                    dignityPoints: 0,
                    decks: [],
                    currentDeckId: null,
                });

                await this.trainerRepo.create(newTrainer.toDocument());
                trainer = newTrainer;
            }

            if (trainer.pigeonBalls <= 0) {
                analysisJob.markAsError(AnalysisJobErrorCode.NO_CAPTURE_STOCK, "No capture stock available");
                await this.analysisJobRepo.update(analysisJob.toDocument());
                return;
            }

            // Update trainer's pigeon ball count
            trainer.pigeonBalls -= 1;
            await this.trainerRepo.update(trainer.toDocument());

            try {
                // Extract coordinates from the filename
                // Format: <latitude>_<longitude>_<fileName>.jpg
                let coordinates: Coordinates = { latitude: 0, longitude: 0 };
                const fileNameParts = pathParts[3].split("_");
                if (fileNameParts.length >= 2) {
                    const latitude = parseFloat(fileNameParts[0]);
                    const longitude = parseFloat(fileNameParts[1]);
                    if (!isNaN(latitude) && !isNaN(longitude)) {
                        coordinates = { latitude, longitude };
                    }
                }

                // Determine the gang based on geolocation
                const gang = this.geoLocationService.determineGang(coordinates, captureDate);
                // Analyze the pigeon picture and get ranked base pigeons
                const rankedPigeons = await this.analysisService.analyzePigeonPicture(imageUrl);

                if (rankedPigeons.length === 0) {
                    analysisJob.markAsError(AnalysisJobErrorCode.NO_MATCHING_PIGEONS, "No matching pigeons found");
                    await this.analysisJobRepo.update(analysisJob.toDocument());
                    // Restore trainer's pigeon ball count
                    trainer.pigeonBalls += 1;
                    await this.trainerRepo.update(trainer.toDocument());
                    return;
                }

                // Use the highest ranked pigeon
                const closestBasePigeon = rankedPigeons[0].basePigeon;
                console.info("Closest base pigeon:", closestBasePigeon.slug, "with score:", rankedPigeons[0].score);

                const rarity = this._getRarity();
                const stats = this.statsService.computeBaseStatsBasedOnRarity(rarity);

                const newPigeon = new Pigeon({
                    id: uuidv4(),
                    ownerId: trainerId,
                    items: {
                        hat: null,
                        mount: null,
                        leftWing: null,
                        rightWing: null,
                    },
                    basePigeonId: closestBasePigeon.id,
                    baseStats: stats,
                    currentFatigue: DEFAULT_MAX_FATIGUE,
                    maxFatigue: DEFAULT_MAX_FATIGUE,
                    capturedAt: captureDate,
                    rarity,
                    originalPicture: {
                        smallThumbnailUrl: imageUrl,
                        originalUrl: imageUrl,
                    },
                    level: 0,
                    trickeryPoints: 0,
                    auraPoints: 0,
                    gang: gang,
                    captureId: captureId,
                });

                await this.pigeonRepo.save(newPigeon.toDocument());

                // Track base pigeon stats for the day
                const captureDay = BasePigeonStats.formatDay(captureDate);
                await this.basePigeonStatsRepo.incrementCapture(
                    captureDay,
                    closestBasePigeon.id,
                    closestBasePigeon.slug,
                );

                const trainerStats = await this.trainerStatsRepo.getByTrainerId(trainerId);
                if (trainerStats) {
                    trainerStats.capturePigeon(newPigeon);
                    await this.trainerStatsRepo.update(trainerStats.toDocument());
                } else {
                    const newTrainerStats = new TrainerStats({
                        id: uuidv4(),
                        trainerId: trainerId,
                        pigeonCount: 0,
                        distinctPigeonCount: 0,
                        basePigeonList: [],
                        legendaryCount: 0,
                        epicCount: 0,
                        rareCount: 0,
                        commonCount: 0,
                        totalCatcherPoints: 0,
                        battlesWon: 0,
                        battlesLost: 0,
                        elo: 1000,
                    });

                    newTrainerStats.capturePigeon(newPigeon);
                    await this.trainerStatsRepo.create(newTrainerStats.toDocument());
                }

                const pigeonWithBasePigeon = {
                    ...newPigeon,
                    basePigeon: closestBasePigeon,
                };

                // Mark the analysis job as finished
                analysisJob.markAsFinished(pigeonWithBasePigeon);
                await this.analysisJobRepo.update(analysisJob.toDocument());
            } catch (error) {
                console.error("Error during pigeon analysis:", error);

                // Determine the appropriate error code based on the error message
                let errorCode = AnalysisJobErrorCode.UNKNOWN_ERROR;
                let errorMessage = "Unknown error occurred";

                if (error instanceof Error) {
                    errorMessage = error.message;
                    if (error.message.includes("Image does not show a bird")) {
                        errorCode = AnalysisJobErrorCode.NOT_A_BIRD;
                    } else if (error.message.includes("Image is too fake")) {
                        errorCode = AnalysisJobErrorCode.IMAGE_TOO_FAKE;
                    } else if (error.message.includes("OpenAI")) {
                        errorCode = AnalysisJobErrorCode.OPENAI_ERROR;
                    } else if (error.message.includes("No matching pigeons")) {
                        errorCode = AnalysisJobErrorCode.NO_MATCHING_PIGEONS;
                    } else {
                        errorCode = AnalysisJobErrorCode.ANALYSIS_FAILED;
                    }
                }

                analysisJob.markAsError(errorCode, errorMessage);
                await this.analysisJobRepo.update(analysisJob.toDocument());

                // Restore trainer's pigeon ball count
                trainer.pigeonBalls += 1;
                await this.trainerRepo.update(trainer.toDocument());
            }
            await this.pingExternalServiceUseCase.execute({
                captureId,
                trainerId,
            });
        } catch (error) {
            console.error("Error in CapturePigeonUseCase:", error);
            // Mark job as error if we couldn't even start processing
            analysisJob.markAsError(AnalysisJobErrorCode.UNKNOWN_ERROR, "Failed to process capture request");
            await this.analysisJobRepo.update(analysisJob.toDocument());
        }
    }

    private _getRarity(): PigeonRarity {
        const random = Math.random();
        if (random < 0.01) {
            return PigeonRarity.LEGENDARY;
        } else if (random < 0.08) {
            return PigeonRarity.EPIC;
        } else if (random < 0.24) {
            return PigeonRarity.RARE;
        } else {
            return PigeonRarity.COMMON;
        }
    }
}
