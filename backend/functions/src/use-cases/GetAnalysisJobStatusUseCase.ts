import { AnalysisJob } from "../domain/entities/AnalysisJob";
import { AnalysisJobRepository } from "../domain/repositories/AnalysisJobRepository";

export class GetAnalysisJobStatusUseCase {
    constructor(private analysisJobRepo: AnalysisJobRepository) {}

    async execute(captureId: string): Promise<AnalysisJob | null> {
        return await this.analysisJobRepo.getByCaptureId(captureId);
    }
}
