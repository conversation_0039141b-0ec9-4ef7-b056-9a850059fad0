import assert from "node:assert";
import { PigeonWithBasePigeon } from "../domain/entities/Pigeon";
import { BasePigeonRepository } from "../domain/repositories/BasePigeonRepository";
import { PigeonRepository } from "../domain/repositories/PigeonRepository";

export class GetPigeonByCaptureIdUseCase {
    constructor(
        private pigeonRepo: PigeonRepository,
        private basePigeonRepo: BasePigeonRepository,
    ) {}

    async execute(captureId: string): Promise<{ pigeon: PigeonWithBasePigeon }> {
        const pigeonDocument = await this.pigeonRepo.getByCaptureId(captureId);
        assert(pigeonDocument, "Pigeon not found");
        const basePigeon = await this.basePigeonRepo.getById(pigeonDocument.basePigeonId);
        assert(basePigeon, "Base pigeon not found");
        const pigeonWithBasePigeon: PigeonWithBasePigeon = {
            ...pigeonDocument,
            basePigeon: basePigeon,
        };
        if (!pigeonDocument) {
            throw new Error("Pigeon not found");
        }
        return {
            pigeon: pigeonWithBasePigeon,
        };
    }
}
