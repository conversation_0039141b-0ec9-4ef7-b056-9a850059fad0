import { AnalysisJob } from "../domain/entities/AnalysisJob";
import { AnalysisJobStatus } from "../domain/enums/AnalysisJobStatus";
import { AnalysisJobRepository } from "../domain/repositories/AnalysisJobRepository";

export interface CreateAnalysisJobRequest {
    captureId: string;
    trainerId: string;
    storageFilePath: string;
}

export class CreateAnalysisJobUseCase {
    constructor(private analysisJobRepo: AnalysisJobRepository) {}

    async execute(request: CreateAnalysisJobRequest): Promise<AnalysisJob> {
        // Check if a job with this captureId already exists
        const existingJob = await this.analysisJobRepo.getByCaptureId(request.captureId);
        if (existingJob) {
            console.log(`Analysis job already exists for captureId: ${request.captureId}`);
            return existingJob;
        }

        // Create the analysis job
        const now = new Date();
        const analysisJob = new AnalysisJob({
            captureId: request.captureId,
            trainerId: request.trainerId,
            storageFilePath: request.storageFilePath,
            status: AnalysisJobStatus.PENDING,
            errorCode: null,
            errorMessage: null,
            createdAt: now,
            updatedAt: now,
            pigeon: null,
        });

        await this.analysisJobRepo.create(analysisJob.toDocument());
        return analysisJob;
    }
}
