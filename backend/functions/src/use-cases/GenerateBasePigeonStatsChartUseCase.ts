import { ChartJSNodeCanvas } from "chartjs-node-canvas";
import { GetBasPigeonStatsUseCase } from "./GetBasePigeonStatsUseCase";
import { ChartConfiguration } from "chart.js";
import fs from "fs";

export class GenerateBasePigeonStatsChartUseCase {
    constructor(private getBasePigeonStatsUseCase: GetBasPigeonStatsUseCase) {}

    async execute(startDate?: Date, endDate?: Date): Promise<string> {
        const width = 400; //px
        const height = 1200; //px
        const backgroundColour = "white"; // Uses https://www.w3schools.com/tags/canvas_fillstyle.asp
        const chartJSNodeCanvas = new ChartJSNodeCanvas({ width, height, backgroundColour });
        const stats = await this.getBasePigeonStatsUseCase.execute(startDate, endDate);
        const labels = stats.map((p) => p.basePigeonSlug);
        const data = stats.map((p) => p.totalCaptures);

        const configuration: ChartConfiguration = {
            type: "bar",
            data: {
                labels,
                datasets: [
                    {
                        label: "Total captures",
                        data,
                        backgroundColor: "rgba(255, 99, 132, 0.2)",
                        borderColor: "rgba(255, 99, 132, 1)",
                        borderWidth: 1,
                    },
                ],
            },
            options: {
                indexAxis: "y",
                scales: {
                    y: {
                        beginAtZero: true,
                    },
                },
            },
        };

        const image = await chartJSNodeCanvas.renderToBuffer(configuration);
        // save local image:
        // fs.writeFileSync("chart.png", image);
        return image.toString("base64");
    }
}
