import assert from "node:assert";
import { PigeonWithBasePigeon } from "../domain/entities/Pigeon";
import { BasePigeonRepository } from "../domain/repositories/BasePigeonRepository";
import { PigeonRepository } from "../domain/repositories/PigeonRepository";
import { TrainerRepository } from "../domain/repositories/TrainerRepository";

export class GetTrainerPigeonsUseCase {
    constructor(
        private trainerRepo: TrainerRepository,
        private pigeonRepo: PigeonRepository,
        private basePigeonRepo: BasePigeonRepository,
    ) {}

    async execute(trainerId: string, limit = 40, offset = 0): Promise<{ pigeons: PigeonWithBasePigeon[] }> {
        const trainer = await this.trainerRepo.getById(trainerId);
        if (!trainer) {
            throw new Error("Trainer not found");
        }
        const pigeonDocuments = await this.pigeonRepo.getByOwnerId(trainerId, limit, offset);

        // Join with base pigeon data
        const pigeonsWithBasePigeon: PigeonWithBasePigeon[] = [];
        const basePigeonIds = pigeonDocuments.map((p) => p.basePigeonId);
        const uniqBasePigeonIds = [...new Set(basePigeonIds)];
        const basePigeons = await this.basePigeonRepo.getByIds(uniqBasePigeonIds);
        for (const pigeon of pigeonDocuments) {
            const basePigeon = basePigeons.find((p) => p.id === pigeon.basePigeonId);
            assert(basePigeon, "Base pigeon not found");
            pigeonsWithBasePigeon.push({
                ...pigeon,
                basePigeon: basePigeon,
            });
        }

        return {
            pigeons: pigeonsWithBasePigeon,
        };
    }
}
