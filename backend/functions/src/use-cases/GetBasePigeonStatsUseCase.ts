import { BasePigeonStatsAggregated } from "../domain/entities/BasePigeonStats";
import { BasePigeonRepository } from "../domain/repositories/BasePigeonRepository";
import { BasePigeonStatsRepository } from "../domain/repositories/BasePigeonStatsRepository";

export class GetBasPigeonStatsUseCase {
    constructor(
        private basePigeonStatsRepo: BasePigeonStatsRepository,
        private basePigeonRepo: BasePigeonRepository,
    ) {}

    async execute(startDate?: Date, endDate?: Date): Promise<BasePigeonStatsAggregated[]> {
        const stats = await this.basePigeonStatsRepo.getAggregatedStats(startDate, endDate);
        const allBasePigeons = await this.basePigeonRepo.getAll();
        const basePigeonMap = new Map(stats.map((p) => [p.basePigeonId, p]));
        const total = stats.reduce((acc, p) => acc + p.totalCaptures, 0);

        return allBasePigeons
            .map((basePigeon) => {
                const basePigeonStats = basePigeonMap.get(basePigeon.id);
                return {
                    basePigeonId: basePigeon.id,
                    basePigeonSlug: basePigeon.slug,
                    totalCaptures: basePigeonStats?.totalCaptures ?? 0,
                    percentage: (basePigeonStats?.totalCaptures ?? 0) / total,
                };
            })
            .sort((a, b) => b.totalCaptures - a.totalCaptures);
    }
}
