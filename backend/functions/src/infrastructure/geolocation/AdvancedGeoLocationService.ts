import { <PERSON><PERSON><PERSON><PERSON> } from "../../domain/enums/PigeonGang";
import { Coordinates, GeoLocationService } from "../../domain/services/GeoLocationService";

/**
 * Constants for the grid system
 */
const GRID_SIZE = 0.05; // Size of each grid square in degrees
const SUB_GRID_DIVISIONS = 8; // 8x8 = 64 sub-squares

// Number of hours in a full rotation cycle for gangs 1-4
const HOURS_IN_CYCLE = 4;

// Number of hours in the full 8-day cycle (192 hours)
const FULL_CYCLE_HOURS = 24 * 8;

// Start date for the cycle (January 1, 2025, at 0:00 AM)
const CYCLE_START_DATE = new Date(2025, 0, 1, 0, 0, 0, 0);

// The 64-entry cycle for Gang FIVE's position
// Each number represents a position in the 8x8 grid (1-64)
// 1 is bottom-left, 64 is top-right
const FIFTH_GANG_CYCLE: number[] = [
    22, 47, 11, 54, 19, 55, 12, 61, 30, 44, 18, 27, 43, 13, 42, 29, 34, 26, 41, 33, 38, 24, 53, 37, 9, 56, 23, 21, 28,
    57, 10, 64, 48, 39, 1, 2, 3, 4, 5, 6, 7, 8, 14, 15, 16, 17, 20, 25, 31, 32, 35, 36, 40, 45, 46, 49, 50, 51, 52, 58,
    59, 60, 62, 63,
];

/**
 * Advanced implementation of GeoLocationService that divides the world into a grid system
 * with dynamic gang territories that change over time.
 */
export class AdvancedGeoLocationService implements GeoLocationService {
    constructor() {
        // No initialization needed
    }

    /**
     * Determines which gang a pigeon belongs to based on its capture coordinates and the date/time.
     * Uses a grid system where:
     * - The world is divided into squares of 0.05° latitude/longitude
     * - Each square is further divided into 64 smaller pieces (8x8 grid)
     * - Gang ONE: top-left quarter
     * - Gang TWO: top-right quarter
     * - Gang THREE: bottom-left quarter
     * - Gang FOUR: bottom-right quarter
     * - Gang FIVE follows a 64-entry cycle that repeats every 192 hours (8 days)
     *
     * @param coordinates The latitude and longitude where the pigeon was captured
     * @param date date to use for calculations
     * @returns The gang that the pigeon belongs to
     */
    determineGang(coordinates: Coordinates, date: Date): PigeonGang {
        const { latitude, longitude } = coordinates;

        // Calculate rotation offset for gangs 1-4 based on hour of day
        const currentHour = date.getHours();
        const rotationOffset = currentHour % HOURS_IN_CYCLE;

        // Calculate which grid square the coordinates fall into
        const gridX = Math.floor(longitude / GRID_SIZE);
        const gridY = Math.floor(latitude / GRID_SIZE);

        // Calculate position within the grid square (0 to 1)
        const posWithinGridX = (longitude - gridX * GRID_SIZE) / GRID_SIZE;
        const posWithinGridY = (latitude - gridY * GRID_SIZE) / GRID_SIZE;

        // Calculate which sub-grid square the coordinates fall into
        const subGridX = Math.floor(posWithinGridX * SUB_GRID_DIVISIONS);
        const subGridY = Math.floor(posWithinGridY * SUB_GRID_DIVISIONS);

        // Get Gang FIVE's sub-grid position for the effective date
        const gangFiveSubPosition = this.getGangFiveSubPositionForDate(date);

        // Check if the coordinates fall within Gang FIVE's territory
        if (subGridX === gangFiveSubPosition.subGridX && subGridY === gangFiveSubPosition.subGridY) {
            return PigeonGang.OBNI;
        }

        // Determine which quarter of the grid the coordinates fall into
        // Apply rotation offset to the gang assignment
        if (posWithinGridX < 0.5 && posWithinGridY < 0.5) {
            // Bottom-left quarter
            return this.getRotatedGang(PigeonGang.MAISON_CONTI, rotationOffset);
        } else if (posWithinGridX >= 0.5 && posWithinGridY < 0.5) {
            // Bottom-right quarter
            return this.getRotatedGang(PigeonGang.LE_SYNDICAT, rotationOffset);
        } else if (posWithinGridX < 0.5 && posWithinGridY >= 0.5) {
            // Top-left quarter
            return this.getRotatedGang(PigeonGang.MAISON_ROSSINI, rotationOffset);
        } else {
            // Top-right quarter
            return this.getRotatedGang(PigeonGang.LES_CRUMBS, rotationOffset);
        }
    }

    /**
     * Gets Gang FIVE's sub-grid position for a specific date and time.
     * The position is determined by the hour within the 192-hour cycle,
     * using the 64-entry FIFTH_GANG_CYCLE array.
     */
    private getGangFiveSubPositionForDate(date: Date): {
        subGridX: number;
        subGridY: number;
    } {
        // Calculate hours since the cycle start
        const hoursSinceCycleStart = this.getHoursSinceCycleStart(date);

        // Calculate the position within the 64-entry cycle
        // Subtract 1 and use modulo 64 as specified in the requirements
        const cyclePosition = FIFTH_GANG_CYCLE[(hoursSinceCycleStart - 1) % 64];

        // Convert the 1-64 position to x,y coordinates in the 8x8 grid
        // Position 1 is bottom-left (0,0), position 64 is top-right (7,7)
        const position = cyclePosition - 1; // Convert to 0-based index
        const subGridX = position % SUB_GRID_DIVISIONS;
        const subGridY = Math.floor(position / SUB_GRID_DIVISIONS);

        return {
            subGridX,
            subGridY,
        };
    }

    /**
     * Calculates the number of hours that have passed since the cycle start date.
     */
    private getHoursSinceCycleStart(date: Date): number {
        // Calculate milliseconds between the dates
        const millisecondsDiff = date.getTime() - CYCLE_START_DATE.getTime();

        // Convert to hours and ensure it's positive using modulo
        const hoursDiff = Math.floor(millisecondsDiff / (1000 * 60 * 60));

        // Use modulo to wrap around the 192-hour cycle
        return ((hoursDiff % FULL_CYCLE_HOURS) + FULL_CYCLE_HOURS) % FULL_CYCLE_HOURS;
    }

    /**
     * Applies the rotation offset to get the current gang for a position.
     * @param baseGang The base gang before rotation
     * @param rotationOffset The current rotation offset (0-3)
     */
    private getRotatedGang(baseGang: PigeonGang, rotationOffset: number): PigeonGang {
        // Convert gang to number (0-3)
        const gangNumber = this.gangToNumber(baseGang);

        // Apply rotation
        const rotatedNumber = (gangNumber + rotationOffset) % 4;

        // Convert back to gang
        return this.numberToGang(rotatedNumber);
    }

    /**
     * Converts a gang to a number (0-3) for rotation calculations.
     */
    private gangToNumber(gang: PigeonGang): number {
        switch (gang) {
            case PigeonGang.MAISON_ROSSINI:
                return 0;
            case PigeonGang.LES_CRUMBS:
                return 1;
            case PigeonGang.MAISON_CONTI:
                return 2;
            case PigeonGang.LE_SYNDICAT:
                return 3;
            default:
                return 0; // Should never happen
        }
    }

    /**
     * Converts a number (0-3) back to a gang.
     */
    private numberToGang(number: number): PigeonGang {
        switch (number) {
            case 0:
                return PigeonGang.MAISON_ROSSINI;
            case 1:
                return PigeonGang.LES_CRUMBS;
            case 2:
                return PigeonGang.MAISON_CONTI;
            case 3:
                return PigeonGang.LE_SYNDICAT;
            default:
                return PigeonGang.MAISON_ROSSINI; // Should never happen
        }
    }
}
