import { PigeonGang } from "../../domain/enums/PigeonGang";
import { Coordinates, GeoLocationService } from "../../domain/services/GeoLocationService";

/**
 * A simple implementation of GeoLocationService that divides the world into 5 regions
 * based on latitude and longitude.
 */
export class SimpleGeoLocationService implements GeoLocationService {
    /**
     * Determines which gang a pigeon belongs to based on its capture coordinates.
     * This is a simple implementation that divides the world into 5 regions:
     * - Gang ONE: North America (longitude < -30, latitude > 0)
     * - Gang TWO: South America (longitude < -30, latitude <= 0)
     * - Gang THREE: Europe and Africa (longitude >= -30 && longitude < 60, any latitude)
     * - Gang FOUR: Asia (longitude >= 60, latitude > 0)
     * - Gang FIVE: Oceania (longitude >= 60, latitude <= 0)
     *
     * @param coordinates The latitude and longitude where the pigeon was captured
     * @param date Optional date parameter (not used in this implementation)
     * @returns The gang that the pigeon belongs to
     */
    determineGang(coordinates: Coordinates, date?: Date): PigeonGang {
        const { latitude, longitude } = coordinates;

        // North America
        if (longitude < -30 && latitude > 0) {
            return PigeonGang.MAISON_ROSSINI;
        }

        // South America
        if (longitude < -30 && latitude <= 0) {
            return PigeonGang.LES_CRUMBS;
        }

        // Europe and Africa
        if (longitude >= -30 && longitude < 60) {
            return PigeonGang.MAISON_CONTI;
        }

        // Asia
        if (longitude >= 60 && latitude > 0) {
            return PigeonGang.LE_SYNDICAT;
        }

        // Oceania
        if (longitude >= 60 && latitude <= 0) {
            return PigeonGang.OBNI;
        }

        // Default to Gang THREE if something goes wrong
        return PigeonGang.MAISON_CONTI;
    }
}
