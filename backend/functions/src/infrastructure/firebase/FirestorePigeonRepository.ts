import * as admin from "firebase-admin";
import { PigeonRepository } from "../../domain/repositories/PigeonRepository";
import { Pigeon, PigeonDocument } from "../../domain/entities/Pigeon";

export class FirestorePigeonRepository implements PigeonRepository {
    private collection = admin.firestore().collection("pigeons");

    async save(pigeon: PigeonDocument): Promise<void> {
        await this.collection.doc(pigeon.id).set(pigeon);
    }

    async getByOwnerId(ownerId: string, limit?: number, offset?: number): Promise<Pigeon[]> {
        const pigeons: Pigeon[] = [];
        let query = this.collection.where("ownerId", "==", ownerId).orderBy("capturedAt", "desc");

        if (offset && offset > 0) {
            query = query.offset(offset);
        }

        if (limit && limit > 0) {
            query = query.limit(limit);
        }

        const snapshot = await query.get();

        snapshot.forEach((doc) => {
            const data = doc.data();
            const pigeon = Pigeon.fromDocument(data as PigeonDocument);

            pigeons.push(pigeon);
        });

        return pigeons;
    }

    async getByCaptureId(captureId: string): Promise<Pigeon | null> {
        const doc = await this.collection.where("captureId", "==", captureId).get();
        if (!doc.docs.length) {
            return null;
        }
        return Pigeon.fromDocument(doc.docs[0].data() as PigeonDocument);
    }
}
