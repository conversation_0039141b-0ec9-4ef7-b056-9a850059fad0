import { TrainerStatsRepository } from "../../domain/repositories/TrainerStatsRepository";
import { TrainerStats, TrainerStatsData, TrainerStatsDocument } from "../../domain/entities/TrainerStats";
import { TrainerStatsOrderBy } from "../../domain/enums/TrainerStatsOrderBy";
import * as admin from "firebase-admin";

export class FirestoreTrainerStatsRepository implements TrainerStatsRepository {
    private collection = admin.firestore().collection("trainerStats");

    async getById(id: string): Promise<TrainerStats | null> {
        const doc = await this.collection.doc(id).get();
        if (!doc.exists) return null;
        return new TrainerStats(doc.data() as TrainerStatsData);
    }

    async getByTrainerId(trainerId: string): Promise<TrainerStats | null> {
        const doc = await this.collection.where("trainerId", "==", trainerId).get();
        if (!doc.docs.length) return null;
        return new TrainerStats(doc.docs[0].data() as TrainerStatsData);
    }

    async update(trainerStats: TrainerStatsDocument): Promise<void> {
        await this.collection.doc(trainerStats.id).set(trainerStats, { merge: true });
    }

    async create(trainerStats: TrainerStatsDocument): Promise<void> {
        await this.collection.doc(trainerStats.id).set(trainerStats);
    }

    async getLeaderBoard(orderBy: TrainerStatsOrderBy, limit: number): Promise<TrainerStats[]> {
        let query = this.collection.orderBy("totalCatcherPoints", "desc").limit(limit);

        switch (orderBy) {
            case TrainerStatsOrderBy.TOTAL_CATCHER_POINTS:
                query = this.collection.orderBy("totalCatcherPoints", "desc").limit(limit);
                break;
            case TrainerStatsOrderBy.TOTAL_PIGEON_COUNT:
                query = this.collection.orderBy("pigeonCount", "desc").limit(limit);
                break;
            case TrainerStatsOrderBy.DISTINCT_PIGEON_COUNT:
                query = this.collection.orderBy("distinctPigeonCount", "desc").limit(limit);
                break;
            case TrainerStatsOrderBy.BATTLES_WON:
                query = this.collection.orderBy("battlesWon", "desc").limit(limit);
                break;
            case TrainerStatsOrderBy.ELO:
                query = this.collection.orderBy("elo", "desc").limit(limit);
                break;
        }

        const snapshot = await query.get();
        return snapshot.docs.map((doc) => new TrainerStats(doc.data() as TrainerStatsData));
    }
}
