import * as admin from "firebase-admin";
import { BasePigeonStatsRepository } from "../../domain/repositories/BasePigeonStatsRepository";
import {
    BasePigeonStats,
    BasePigeonStatsData,
    BasePigeonStatsDocument,
    BasePigeonStatsAggregated,
} from "../../domain/entities/BasePigeonStats";

export class FirestoreBasePigeonStatsRepository implements BasePigeonStatsRepository {
    private collection = admin.firestore().collection("basePigeonStats");

    async incrementCapture(day: string, basePigeonId: string, basePigeonSlug: string): Promise<void> {
        const id = BasePigeonStats.generateId(day, basePigeonId);
        const docRef = this.collection.doc(id);

        await admin.firestore().runTransaction(async (transaction) => {
            const doc = await transaction.get(docRef);

            if (doc.exists) {
                transaction.update(docRef, {
                    totalCaptures: admin.firestore.FieldValue.increment(1),
                });
            } else {
                const newStats: BasePigeonStatsDocument = {
                    id,
                    day,
                    basePigeonId,
                    basePigeonSlug,
                    totalCaptures: 1,
                };
                transaction.set(docRef, newStats);
            }
        });
    }

    async getByDayAndBasePigeon(day: string, basePigeonId: string): Promise<BasePigeonStats | null> {
        const id = BasePigeonStats.generateId(day, basePigeonId);
        const doc = await this.collection.doc(id).get();

        if (!doc.exists) {
            return null;
        }

        return new BasePigeonStats(doc.data() as BasePigeonStatsData);
    }

    async getAggregatedStats(startDate?: Date, endDate?: Date): Promise<BasePigeonStatsAggregated[]> {
        let query: admin.firestore.Query = this.collection;

        if (startDate) {
            const startDay = BasePigeonStats.formatDay(startDate);
            query = query.where("day", ">=", startDay);
        }

        if (endDate) {
            const endDay = BasePigeonStats.formatDay(endDate);
            query = query.where("day", "<=", endDay);
        }

        const snapshot = await query.get();

        const aggregatedMap = new Map<string, BasePigeonStatsAggregated>();

        snapshot.forEach((doc) => {
            const data = doc.data() as BasePigeonStatsData;
            const existing = aggregatedMap.get(data.basePigeonId);

            if (existing) {
                existing.totalCaptures += data.totalCaptures;
            } else {
                aggregatedMap.set(data.basePigeonId, {
                    basePigeonId: data.basePigeonId,
                    basePigeonSlug: data.basePigeonSlug,
                    totalCaptures: data.totalCaptures,
                });
            }
        });

        return Array.from(aggregatedMap.values()).sort((a, b) => b.totalCaptures - a.totalCaptures);
    }

    async create(stats: BasePigeonStatsDocument): Promise<void> {
        await this.collection.doc(stats.id).set(stats);
    }

    async update(stats: BasePigeonStatsDocument): Promise<void> {
        await this.collection.doc(stats.id).set(stats, { merge: true });
    }

    async getByDay(day: string): Promise<BasePigeonStats[]> {
        const snapshot = await this.collection.where("day", "==", day).get();

        return snapshot.docs.map((doc) => new BasePigeonStats(doc.data() as BasePigeonStatsData));
    }
}
