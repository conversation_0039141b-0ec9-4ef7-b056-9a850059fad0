import { TrainerRepository } from "../../domain/repositories/TrainerRepository";
import { Trainer, TrainerData, TrainerDocument } from "../../domain/entities/Trainer";
import * as admin from "firebase-admin";
import { PopulatedDeck } from "../../domain/entities/Deck";

export class FirestoreTrainerRepository implements TrainerRepository {
    private collection = admin.firestore().collection("trainers");

    async getById(id: string): Promise<Trainer | null> {
        const doc = await this.collection.doc(id).get();
        if (!doc.exists) return null;
        return new Trainer(doc.data() as TrainerData);
    }

    async update(trainer: TrainerDocument): Promise<void> {
        await this.collection.doc(trainer.id).set(trainer, { merge: true });
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async getCurrentDeck(_trainerId: string): Promise<PopulatedDeck | null> {
        return null;
    }

    async create(trainer: TrainerDocument): Promise<void> {
        await this.collection.doc(trainer.id).set(trainer);
    }

    async getByIds(ids: string[]): Promise<Trainer[]> {
        const trainers: Trainer[] = [];
        const snapshot = await this.collection.where(admin.firestore.FieldPath.documentId(), "in", ids).get();
        snapshot.forEach((doc) => {
            const trainer = doc.data() as TrainerData;
            trainers.push(new Trainer(trainer));
        });
        return trainers;
    }
}
