/* eslint-disable max-len */
import { AnalysisService, RankedBasePigeon } from "../../domain/services/AnalysisService";
import { BasePigeonRepository } from "../../domain/repositories/BasePigeonRepository";
import OpenAI from "openai";
import { BirdType } from "../../domain/enums/BirdType";
import { PigeonDistinctiveness } from "../../domain/enums/PigeonDistinctiveness";
import { shuffle } from "lodash";
import assert from "node:assert";

// Type for the unified OpenAI call response that includes both analysis and matching
export interface PigeonAnalysisAndMatch {
    // Analysis fields
    looks_like_a_screenshot: number; // 1 to 10
    is_bird: boolean;
    bird_type: BirdType; // "pigeon" | "crow" | "seagull" | "sparrow" | "magpie"
    is_baby_pigeon: boolean;
    is_dead: boolean;

    // Matching fields
    matches: Array<{
        base_pigeon_id: string;
        score: number; // 0-100 score where 100 is perfect match
        reasoning: string; // Brief explanation of why this pigeon matches
    }>;
}

export class OpenAiServiceV3 implements AnalysisService {
    client: OpenAI;

    constructor(private basePigeonRepository: BasePigeonRepository) {
        this.client = new OpenAI();
    }

    async analyzePigeonPicture(imageUrl: string): Promise<RankedBasePigeon[]> {
        console.info(`Analyzing pigeon picture at ${imageUrl} using OpenAiServiceV3`, new Date());

        try {
            const allBasePigeons = await this.basePigeonRepository.getAll();

            // Create the unified schema that includes both analysis and matching
            const unifiedAnalysisSchema = {
                type: "object",
                properties: {
                    // Analysis properties
                    looks_like_a_screenshot: {
                        type: "number",
                        description: "Rating from 1-10 where 1 means original picture, 10 means definitely screenshot",
                    },
                    is_bird: {
                        type: "boolean",
                        description: "Whether the image shows a bird",
                    },
                    bird_type: {
                        type: "string",
                        enum: Object.values(BirdType),
                        description: "The type of bird if identifiable",
                    },
                    is_baby_pigeon: {
                        type: "boolean",
                        description: "Whether the bird appears to be a baby pigeon",
                    },
                    is_dead: {
                        type: "boolean",
                        description: "Whether the bird appears to be dead",
                    },
                    // Matching properties
                    matches: {
                        type: "array",
                        items: {
                            type: "object",
                            properties: {
                                base_pigeon_id: {
                                    type: "string",
                                    enum: allBasePigeons.map((pigeon) => pigeon.id),
                                    description: "Id of the pigeon from the list of provided pigeons",
                                },
                                score: {
                                    type: "number",
                                    description: "Score from 0-100 where 100 is a perfect match",
                                },
                                reasoning: {
                                    type: "string",
                                    description: "Brief explanation of why this pigeon matches (max 50 words)",
                                },
                            },
                            required: ["base_pigeon_id", "score", "reasoning"],
                            additionalProperties: false,
                        },
                        description: "Top 3 matching pigeons with scores and reasoning",
                    },
                },
                required: ["looks_like_a_screenshot", "is_bird", "bird_type", "is_baby_pigeon", "is_dead", "matches"],
                additionalProperties: false,
            };

            // Create the unified prompt that handles both analysis and matching
            const unifiedPrompt = `You are an expert pigeon photo analyzer. Your task is to:

1. ANALYZE the pigeon photo and extract key characteristics
2. MATCH the pigeon against a list of reference pigeons

ANALYSIS TASK:
Analyze the image and provide:
- looks_like_a_screenshot: 1-10 rating (1=original photo, 10=definitely screenshot)
- is_bird: true/false
- bird_type: pigeon/crow/seagull/sparrow/magpie. If it's anything else, just put is_bird to false.
- is_baby_pigeon: true/false
- is_dead: true/false

For description, focus on:
- Exact color tones (e.g., charcoal grey, iridescent green, rusty brown)
- Wing patterns (e.g., checker, bar, marbled, solid)
- Head coloration and patterns
- Color alternation, gradients, or transitions
- Spots, patches, or unique markings
- Note if it might be a turtledove

Do NOT focus on eyes, paws, dirtiness, beak, fatness, etc.

MATCHING TASK:
Compare the analyzed pigeon against these reference pigeons and provide the top 3 matches:

${shuffle(allBasePigeons)
    .map((pigeon) => `${pigeon.id}: ${pigeon.typicalAnalysis.description}`)
    .join("\n")}

For each match, provide:
- base_pigeon_id: The id from the list above
- score: 0-100 where 100 is perfect match
- reasoning: Brief explanation (max 50 words) of why this pigeon matches

Focus on matching based on the visual characteristics you analyzed, prioritizing color patterns, wing patterns, head coloration, and overall appearance.

Respond ONLY with the JSON structure matching the provided schema.`;

            console.info("Starting unified analysis and matching at ", new Date());

            const response = await this.client.responses.create({
                model: "gpt-4.1-mini",
                input: [
                    {
                        role: "system",
                        content: unifiedPrompt,
                    },
                    {
                        role: "user",
                        content: [
                            {
                                type: "input_text",
                                text: "Please analyze this bird photo and match it against the reference pigeons.",
                            },
                            {
                                type: "input_image",
                                detail: "low",
                                image_url: imageUrl,
                            },
                        ],
                    },
                ],
                text: {
                    format: {
                        type: "json_schema",
                        name: "pigeon_analysis_and_match",
                        schema: unifiedAnalysisSchema,
                        strict: true,
                    },
                },
            });

            console.info("Unified analysis and matching completed at ", new Date());

            if (response.error) {
                throw new Error(`OpenAI error: ${response.error.message}`);
            }
            if (!response.output_text) {
                throw new Error("No response from OpenAI for unified analysis");
            }

            const result = JSON.parse(response.output_text) as PigeonAnalysisAndMatch;
            console.info("Unified analysis result:", result);

            // Handle special cases based on analysis
            if (result.looks_like_a_screenshot >= 8) {
                throw new Error("Image is too fake to analyze");
            }
            if (!result.is_bird) {
                throw new Error("Image does not show a bird");
            }

            if (result.is_dead) {
                const sonny = allBasePigeons.find((pigeon) => pigeon.slug === "sonny");
                assert(sonny);
                return [{ basePigeon: sonny, score: 100 }];
            }
            if (result.is_baby_pigeon) {
                const joe = allBasePigeons.find((pigeon) => pigeon.slug === "joe");
                assert(joe);
                return [{ basePigeon: joe, score: 100 }];
            }
            if (result.bird_type !== BirdType.PIGEON) {
                const otherBird = allBasePigeons.find((pigeon) => pigeon.typicalAnalysis.birdType === result.bird_type);
                if (!otherBird) {
                    throw new Error(`No base pigeon found for ${result.bird_type}`);
                }
                return [{ basePigeon: otherBird, score: 100 }];
            }

            // Convert matches to RankedBasePigeon format
            const rankedPigeons = result.matches.map((match) => {
                const basePigeon = allBasePigeons.find((pigeon) => pigeon.id === match.base_pigeon_id);
                if (!basePigeon) {
                    throw new Error(`Base pigeon not found for id ${match.base_pigeon_id}`);
                }
                return {
                    basePigeon,
                    score: match.score,
                };
            });

            return rankedPigeons;
        } catch (error) {
            console.error("Error analyzing pigeon picture with V3:", error);
            throw error;
        }
    }
}
