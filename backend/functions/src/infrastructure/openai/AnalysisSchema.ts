export const analysisSchema = {
    type: "object",
    properties: {
        // General Info
        looks_like_a_screenshot: {
            type: ["number", "null"],
            minimum: 1,
            maximum: 10,
            description: "1–10. 10 means definitely a screenshot of a screen (anti-cheat).",
        },
        image_quality: {
            type: ["number", "null"],
            minimum: 1,
            maximum: 10,
            description: "1–10 image clarity of the bird.",
        },
        is_bird: { type: ["boolean", "null"] },
        species: {
            type: ["string", "null"],
            enum: [
                "columba_livia",
                "turtledove",
                "wood_pigeon",
                "dove",
                "sparrow",
                "seagull",
                "gull",
                "crow",
                "raven",
                "starling",
                "hen",
                "rooster",
                "duck",
                "magpie",
                "other",
            ],
        },
        is_baby_pigeon: { type: ["boolean", "null"] },
        is_dead: { type: ["boolean", "null"] },
        distinctiveness: {
            type: ["string", "null"],
            enum: ["common", "unusual", "rare"],
        },
        description: {
            type: ["string", "null"],
            description: "≤50 words, plumage only (colors, transitions, markings).",
        },
        confidence: {
            type: ["number", "null"],
            minimum: 1,
            maximum: 10,
            description: "1–10 analysis confidence.",
        },
        behavior: { type: ["string", "null"], description: "Visible action (e.g. eating, resting)." },
        context: { type: ["string", "null"], description: "≤10 words brief surroundings." },

        // Pigeon-Specific Traits (only if species === "columba_livia")
        base_color: {
            type: ["string", "null"],
            enum: ["blue", "ash-red", "brown", "other"],
        },
        main_pattern: {
            type: ["string", "null"],
            enum: ["bar", "checker", "t-check", "barless", "spread", "solid", "other"],
        },
        is_spread: { type: ["boolean", "null"] },
        spread_level: { type: ["string", "null"], enum: ["none", "partial", "full"] },
        is_dilute: { type: ["boolean", "null"] },
        dilute_level: { type: ["string", "null"], enum: ["none", "light", "full"] },
        is_piebald: { type: ["boolean", "null"] },
        piebald_level: {
            type: ["string", "null"],
            enum: ["none", "light", "intermediate", "heavy", "full_white"],
        },
        piebald_distribution: {
            type: ["object", "null"],
            properties: {
                head: { type: ["string", "null"], enum: ["none", "light", "intermediate", "heavy", "full"] },
                neck: { type: ["string", "null"], enum: ["none", "light", "intermediate", "heavy", "full"] },
                back: { type: ["string", "null"], enum: ["none", "light", "intermediate", "heavy", "full"] },
                wings: { type: ["string", "null"], enum: ["none", "light", "intermediate", "heavy", "full"] },
                tail: { type: ["string", "null"], enum: ["none", "light", "intermediate", "heavy", "full"] },
                belly: { type: ["string", "null"], enum: ["none", "light", "intermediate", "heavy", "full"] },
            },
            required: ["head", "neck", "back", "wings", "tail", "belly"],
            additionalProperties: false,
        },
        head_pattern: { type: ["string", "null"], enum: ["helmet", "baldhead", "none"] },
        neck_pattern: { type: ["string", "null"], enum: ["white_collar", "none"] },
        body_white_pattern: {
            type: ["string", "null"],
            enum: ["saddle", "shield", "rump", "vent", "mottled", "none", "other"],
        },
        has_grizzle: { type: ["boolean", "null"] },
        has_recessive_red: { type: ["boolean", "null"] },
        visible_color: {
            type: ["string", "null"],
            enum: ["blue", "black", "brown", "ash-red", "silver", "white", "mixed", "other"],
        },
        iridescence_level: { type: ["string", "null"], enum: ["none", "low", "medium", "high"] },
        tail_color: { type: ["string", "null"], enum: ["normal", "white", "black", "mixed"] },
        special_face_mark: { type: ["string", "null"] },
        wing_tip_color: { type: ["string", "null"], enum: ["black", "dark", "grey", "light", "white"] },
        dirtiness_level: { type: ["string", "null"], enum: ["clean", "average", "dirty"] },
        body_size: { type: ["string", "null"], enum: ["slim", "average", "plump", "very_plump"] },
        has_broken_leg: { type: ["boolean", "null"] },
        has_banded_leg: { type: ["boolean", "null"] },
        other_notable_trait: { type: ["string", "null"] },
    },
    required: [
        "looks_like_a_screenshot",
        "image_quality",
        "is_bird",
        "species",
        "distinctiveness",
        "description",
        "confidence",
    ],
    additionalProperties: false,
};
