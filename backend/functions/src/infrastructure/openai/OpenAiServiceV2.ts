/* eslint-disable max-len */
import { AnalysisService, RankedBasePigeon } from "../../domain/services/AnalysisService";
import { BasePigeonRepository } from "../../domain/repositories/BasePigeonRepository";
import OpenAI from "openai";
import { BirdType } from "../../domain/enums/BirdType";
import { PigeonDistinctiveness } from "../../domain/enums/PigeonDistinctiveness";
import { shuffle } from "lodash";
import assert from "node:assert";

// Type for the first OpenAI call response
export interface PigeonInitialAnalysis {
    looks_like_a_screenshot: number; // 1 to 10
    is_bird: boolean;
    bird_type: string; // "pigeon" | "crow" | "seagull" | "sparrow" | "magpie"
    is_baby_pigeon: boolean;
    is_dead: boolean;
    distinctiveness: string; // "COMMON", "UNUSUAL", "RARE"
    description: string; // Short description (max 80 words) of the pigeon's plumage and patterns
}

// Type for the second OpenAI call response with scores for each pigeon
interface PigeonMatchResult {
    matches: Array<{
        pigeon_slug: string;
        score: number; // 0-100 score where 100 is perfect match
    }>;
}

export class OpenAiServiceV2 implements AnalysisService {
    client: OpenAI;

    constructor(private basePigeonRepository: BasePigeonRepository) {
        this.client = new OpenAI();
    }

    /**
     * Gets the initial analysis from OpenAI without matching against base pigeons
     * @param imageUrl Complete url to the image in Firebase Storage
     * @returns Promise with the full initial analysis
     */
    async getInitialAnalysis(imageUrl: string): Promise<PigeonInitialAnalysis> {
        console.info(`Getting initial analysis for pigeon picture at ${imageUrl}`);

        try {
            const initialAnalysisSchema = {
                type: "object",
                properties: {
                    looks_like_a_screenshot: {
                        type: "number",
                        description: "Rating from 1-10 where 1 means original picture, 10 means definitely screenshot",
                    },
                    is_bird: {
                        type: "boolean",
                        description: "Whether the image shows a bird",
                    },
                    bird_type: {
                        type: "string",
                        enum: Object.values(BirdType),
                        description: "The type of bird if identifiable",
                    },
                    is_baby_pigeon: {
                        type: "boolean",
                        description: "Whether the bird appears to be a baby pigeon",
                    },
                    is_dead: {
                        type: "boolean",
                        description: "Whether the bird appears to be dead",
                    },
                    distinctiveness: {
                        type: "string",
                        enum: Object.values(PigeonDistinctiveness),
                        description:
                            "How distinctive the pigeon's appearance is: COMMON (standard coloration), UNUSUAL (somewhat distinctive), or RARE (very distinctive/unique)",
                    },
                    description: {
                        type: "string",
                        description:
                            "A detailed description (max 80 words) of the pigeon's plumage and patterns, focusing on coloration and distinctive features across the entire body",
                    },
                },
                required: [
                    "looks_like_a_screenshot",
                    "is_bird",
                    "bird_type",
                    "is_baby_pigeon",
                    "is_dead",
                    "distinctiveness",
                    "description",
                ],
                additionalProperties: false,
            };

            const initialResponse = await this.client.responses.create({
                model: "gpt-4.1-mini",
                input: [
                    {
                        role: "system",
                        content: `Analyze this bird image. Return:

looks_like_a_screenshot: 1 to 10
is_bird: true/false
bird_type: pigeon/crow/seagull/sparrow/magpie
is_baby_pigeon: true/false
is_dead: true/false
distinctiveness: COMMON/UNUSUAL/RARE (how distinctive the pigeon's appearance is)
description: A detailed description (max 80 words) of the pigeon's plumage and patterns

For distinctiveness:
- COMMON: Standard city pigeon coloration with typical grey body, dark head, and possibly standard wing bars
- UNUSUAL: Somewhat distinctive coloration or patterns that differ from standard pigeons
- RARE: Very distinctive or unique coloration, rare patterns, or unusual features

For the description part, describe the pigeon's head & plumage with maximum precision, including:

It might be a turtledove, if so, state it.
Exact color tones (e.g. charcoal grey, iridescent green, rusty brown)
Wing patterns (e.g., checker, bar, marbled, solid)
Head (e.g, spotted, uniform, different from the body)
Color alternation, gradients, or transitions
Spots, patches, or unique markings (especially if localized: e.g., "white spot behind the head," lighter wing tips")

Do NOT give importance to details such as eyes, paws, dirtiness, beak, fatness etc.

Respond ONLY with the JSON structure matching the provided schema.
Be PRECISE and CONCISE.
`,
                    },
                    {
                        role: "user",
                        content: [
                            { type: "input_text", text: "Please analyze this bird photo in detail." },
                            {
                                type: "input_image",
                                detail: "low",
                                image_url: imageUrl,
                            },
                        ],
                    },
                ],
                text: {
                    format: {
                        type: "json_schema",
                        name: "pigeon_initial_analysis",
                        schema: initialAnalysisSchema,
                        strict: true,
                    },
                },
            });

            // Parse the initial analysis
            if (initialResponse.error) {
                throw new Error(`OpenAI error: ${initialResponse.error.message}`);
            }
            if (!initialResponse.output_text) {
                throw new Error("No response from OpenAI for initial analysis");
            }
            const initialAnalysis = JSON.parse(initialResponse.output_text) as PigeonInitialAnalysis;
            console.log("Initial analysis:", initialAnalysis);

            return initialAnalysis;
        } catch (error) {
            console.error("Error getting initial pigeon analysis:", error);
            throw error;
        }
    }

    async analyzePigeonPicture(imageUrl: string): Promise<RankedBasePigeon[]> {
        console.info(`Analyzing pigeon picture at ${imageUrl} using OpenAiServiceV2`, new Date());

        try {
            const initialAnalysis = await this.getInitialAnalysis(imageUrl);

            console.info("Initial analysis completed at ", new Date());

            if (initialAnalysis.looks_like_a_screenshot >= 8) {
                throw new Error("Image is too fake to analyze");
            }
            if (!initialAnalysis.is_bird) {
                throw new Error("Image does not show a bird");
            }
            const allBasePigeons = await this.basePigeonRepository.getAll();

            if (initialAnalysis.is_dead) {
                const sonny = allBasePigeons.find((pigeon) => pigeon.slug === "sonny");
                assert(sonny);
                return [{ basePigeon: sonny, score: 100 }];
            }
            if (initialAnalysis.is_baby_pigeon) {
                const joe = allBasePigeons.find((pigeon) => pigeon.slug === "joe");
                assert(joe);
                return [{ basePigeon: joe, score: 100 }];
            }
            if (initialAnalysis.bird_type !== BirdType.PIGEON) {
                const otherBird = allBasePigeons.find(
                    (pigeon) => pigeon.typicalAnalysis.birdType === initialAnalysis.bird_type,
                );
                if (!otherBird) {
                    throw new Error(`No base pigeon found for ${initialAnalysis.bird_type}`);
                }
                return [{ basePigeon: otherBird, score: 100 }];
            }

            // Use all base pigeons instead of filtering by distinctiveness
            // This allows for more flexibility in matching
            const filteredBasePigeons = allBasePigeons.filter(
                (p) =>
                    p.typicalAnalysis.distinctiveness === initialAnalysis.distinctiveness ||
                    p.typicalAnalysis.distinctiveness === PigeonDistinctiveness.UNUSUAL,
            );

            const pigeonMatchSchema = {
                type: "object",
                properties: {
                    matches: {
                        type: "array",
                        items: {
                            type: "object",
                            properties: {
                                pigeon_slug: {
                                    type: "string",
                                    enum: filteredBasePigeons.map((pigeon) => pigeon.slug),
                                    description: "Slug of the pigeon from the list of provided pigeons",
                                },
                                score: {
                                    type: "number",
                                    description: "Score from 0-100 where 100 is a perfect match",
                                },
                            },
                            required: ["pigeon_slug", "score"],
                            additionalProperties: false,
                        },
                    },
                },
                required: ["matches"],
                additionalProperties: false,
            };

            const pigeonMatchPrompt = `
You are an expert pigeon photo analyzer. Your task is to compare a pigeon photo against a list of pigeon descriptions and determine which pigeon(s) the photo most closely resembles.

The pigeon photo has already been analyzed and the following details have been extracted:
${JSON.stringify(initialAnalysis.description)}
Here is a list of descriptions that might match the pigeon in the photo:
${shuffle(filteredBasePigeons)
    .map((pigeon) => `${pigeon.slug}: ${pigeon.typicalAnalysis.description}`)
    .join("\n")}

Please provide the top 3 pigeons that match the photo, along with a score for each pigeon. The score should be a number between 0 and 100, where 100 means the pigeon description matches the photo perfectly.
`;

            console.info("Starting second analysis at ", new Date());

            const matchResponse = await this.client.responses.create({
                model: "gpt-4.1-mini",
                input: [
                    {
                        role: "system",
                        content: pigeonMatchPrompt,
                    },
                ],
                text: {
                    format: {
                        type: "json_schema",
                        name: "pigeon_match_result",
                        schema: pigeonMatchSchema,
                        strict: true,
                    },
                },
            });

            console.info("Second analysis completed at ", new Date());

            if (matchResponse?.error) {
                throw new Error(`OpenAI error: ${matchResponse.error.message}`);
            }
            if (!matchResponse.output_text) {
                throw new Error("No response from OpenAI for pigeon match");
            }

            const matchResult = JSON.parse(matchResponse.output_text) as PigeonMatchResult;
            console.info("Match result:", matchResult);

            const rankedPigeons = matchResult.matches.map((match) => {
                const basePigeon = allBasePigeons.find((pigeon) => pigeon.slug === match.pigeon_slug);
                if (!basePigeon) {
                    throw new Error(`Base pigeon not found for ID ${match.pigeon_slug}`);
                }
                return {
                    basePigeon,
                    score: match.score,
                };
            });

            return rankedPigeons;
        } catch (error) {
            console.error("Error analyzing pigeon picture:", error);
            throw error;
        }
    }

    /**
     * This is a temporary method to get a simple analysis of a pigeon picture
     */
    async getSimplePigeonAnalysis(imageUrl: string): Promise<PigeonInitialAnalysis> {
        try {
            const simpleAnalysisSchema = {
                type: "object",
                properties: {
                    distinctiveness: {
                        type: "string",
                        enum: Object.values(PigeonDistinctiveness),
                        description:
                            "How distinctive the pigeon's appearance is: COMMON (standard coloration), UNUSUAL (somewhat distinctive), or RARE (very distinctive/unique)",
                    },
                    description: {
                        type: "string",
                        description:
                            "A detailed description (max 80 words) of the pigeon's plumage and patterns, focusing on coloration and distinctive features across the entire body",
                    },
                },
                required: ["distinctiveness", "description"],
                additionalProperties: false,
            };

            const initialResponse = await this.client.responses.create({
                model: "gpt-4.1-mini",
                input: [
                    {
                        role: "system",
                        content: `Analyze this bird image. Return:
distinctiveness: COMMON/UNUSUAL/RARE (how distinctive the pigeon's appearance is)
description: A detailed description (max 80 words) of the pigeon's plumage and patterns

For distinctiveness:
- COMMON: Standard city pigeon coloration with typical grey body, dark head, and possibly standard wing bars
- UNUSUAL: Somewhat distinctive coloration or patterns that differ from standard pigeons
- RARE: Very distinctive or unique coloration, rare patterns, or unusual features

For the description part, describe the pigeon's head & plumage with maximum precision, including:

Exact color tones (e.g., charcoal grey, iridescent green, rusty brown)
Wing patterns (e.g., checker, bar, marbled, solid)
Head (e.g, spotted, uniform, different from the body)
Color alternation, gradients, or transitions
Spots, patches, or unique markings (especially if localized: e.g., "white spot behind the head," lighter wing tips")

Do NOT give importance to details such as eyes, paws, dirtiness, beak, fatness etc.

Respond ONLY with the JSON structure matching the provided schema.
Be PRECISE and CONCISE.
`,
                    },
                    {
                        role: "user",
                        content: [
                            { type: "input_text", text: "Please analyze this bird photo in detail." },
                            {
                                type: "input_image",
                                detail: "low",
                                image_url: imageUrl,
                            },
                        ],
                    },
                ],
                text: {
                    format: {
                        type: "json_schema",
                        name: "pigeon_simple_analysis",
                        schema: simpleAnalysisSchema,
                        strict: true,
                    },
                },
            });

            // Parse the initial analysis
            if (initialResponse.error) {
                throw new Error(`OpenAI error: ${initialResponse.error.message}`);
            }
            if (!initialResponse.output_text) {
                throw new Error("No response from OpenAI for initial analysis");
            }
            const simpleAnalysis = JSON.parse(initialResponse.output_text) as PigeonInitialAnalysis;
            console.log("Initial analysis:", simpleAnalysis);
            return simpleAnalysis;
        } catch (error) {
            console.error("Error getting initial pigeon analysis:", error);
            throw error;
        }
    }
}
