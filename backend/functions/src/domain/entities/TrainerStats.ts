import { PigeonRarity } from "../enums/PigeonRarity";

export interface TrainerStatsData {
    id: string;
    trainerId: string;
    pigeonCount: number;
    distinctPigeonCount: number;
    basePigeonList: string[];
    legendaryCount: number;
    epicCount: number;
    rareCount: number;
    commonCount: number;
    totalCatcherPoints: number;
    battlesWon: number;
    battlesLost: number;
    elo: number;
}

export class TrainerStats implements TrainerStatsData {
    id: string;
    trainerId: string;
    pigeonCount: number;
    distinctPigeonCount: number;
    basePigeonList: string[];
    legendaryCount: number;
    epicCount: number;
    rareCount: number;
    commonCount: number;
    totalCatcherPoints: number;
    battlesWon: number;
    battlesLost: number;
    elo: number;

    constructor(data: TrainerStatsData) {
        this.id = data.id;
        this.trainerId = data.trainerId;
        this.pigeonCount = data.pigeonCount;
        this.distinctPigeonCount = data.distinctPigeonCount;
        this.basePigeonList = data.basePigeonList;
        this.legendaryCount = data.legendaryCount;
        this.epicCount = data.epicCount;
        this.rareCount = data.rareCount;
        this.commonCount = data.commonCount;
        this.totalCatcherPoints = data.totalCatcherPoints;
        this.battlesWon = data.battlesWon;
        this.battlesLost = data.battlesLost;
        this.elo = data.elo;
    }

    actualizeCatcherPoints(): void {
        const basePoints =
            this.legendaryCount * 1000 + this.epicCount * 500 + this.rareCount * 100 + this.commonCount * 10;

        const diversityMultiplier = 1 + this.distinctPigeonCount * 0.05;

        this.totalCatcherPoints = Math.round(basePoints * diversityMultiplier);
    }

    capturePigeon(pigeon: { rarity: PigeonRarity; basePigeonId: string }): void {
        this.pigeonCount += 1;

        if (!this.basePigeonList.includes(pigeon.basePigeonId)) {
            this.basePigeonList.push(pigeon.basePigeonId);
            this.distinctPigeonCount += 1;
        }

        switch (pigeon.rarity) {
            case PigeonRarity.COMMON:
                this.commonCount += 1;
                break;
            case PigeonRarity.RARE:
                this.rareCount += 1;
                break;
            case PigeonRarity.EPIC:
                this.epicCount += 1;
                break;
            case PigeonRarity.LEGENDARY:
                this.legendaryCount += 1;
                break;
        }

        this.actualizeCatcherPoints();
    }

    toDocument(): TrainerStatsDocument {
        return {
            id: this.id,
            trainerId: this.trainerId,
            pigeonCount: this.pigeonCount,
            distinctPigeonCount: this.distinctPigeonCount,
            basePigeonList: this.basePigeonList,
            legendaryCount: this.legendaryCount,
            epicCount: this.epicCount,
            rareCount: this.rareCount,
            commonCount: this.commonCount,
            totalCatcherPoints: this.totalCatcherPoints,
            battlesWon: this.battlesWon,
            battlesLost: this.battlesLost,
            elo: this.elo,
        };
    }
}

export interface TrainerStatsDocument {
    id: string;
    trainerId: string;
    pigeonCount: number;
    distinctPigeonCount: number;
    basePigeonList: string[];
    legendaryCount: number;
    epicCount: number;
    rareCount: number;
    commonCount: number;
    totalCatcherPoints: number;
    battlesWon: number;
    battlesLost: number;
    elo: number;
}
