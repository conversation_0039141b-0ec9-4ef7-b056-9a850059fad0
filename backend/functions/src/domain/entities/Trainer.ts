import { Deck } from "./Deck";

export interface TrainerData {
    id: string;
    username: string;
    pigeonBalls: number;
    dignityPoints: number;
    decks: Deck[];
    currentDeckId: string | null;
}

export class Trainer {
    id: string;
    username: string;
    pigeonBalls: number;
    dignityPoints: number;
    decks: Deck[];
    currentDeckId: string | null;

    constructor(data: TrainerData) {
        this.id = data.id;
        this.username = data.username;
        this.pigeonBalls = data.pigeonBalls;
        this.dignityPoints = data.dignityPoints;
        this.decks = data.decks ?? [];
        this.currentDeckId = data.currentDeckId ?? null;
    }

    usedPigeonBall(): void {
        if (this.pigeonBalls > 0) {
            this.pigeonBalls--;
        } else {
            throw new Error("No more pigeon balls left");
        }
    }

    toDocument(): TrainerDocument {
        return {
            id: this.id,
            username: this.username,
            pigeonBalls: this.pigeonBalls,
            dignityPoints: this.dignityPoints,
            decks: this.decks,
            currentDeckId: this.currentDeckId,
        };
    }
}

export interface TrainerDocument {
    id: string;
    username: string;
    pigeonBalls: number;
    dignityPoints: number;
    decks: Deck[];
    currentDeckId: string | null;
}

export const defaultTrainer = {
    id: "randomId",
    username: "Cousinho",
    pigeonBalls: 3,
    dignityPoints: 0,
    decks: [],
    currentDeckId: null,
};
