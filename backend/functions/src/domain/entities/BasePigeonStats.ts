export interface BasePigeonStatsData {
    id: string;
    day: string; // Format: YYYY-MM-DD
    basePigeonId: string;
    basePigeonSlug: string;
    totalCaptures: number;
}

export class BasePigeonStats implements BasePigeonStatsData {
    id: string;
    day: string;
    basePigeonId: string;
    basePigeonSlug: string;
    totalCaptures: number;

    constructor(data: BasePigeonStatsData) {
        this.id = data.id;
        this.day = data.day;
        this.basePigeonId = data.basePigeonId;
        this.basePigeonSlug = data.basePigeonSlug;
        this.totalCaptures = data.totalCaptures;
    }

    /**
     * Increment the total captures by 1
     */
    incrementCaptures(): void {
        this.totalCaptures += 1;
    }

    /**
     * Convert to document format for Firestore storage
     */
    toDocument(): BasePigeonStatsDocument {
        return {
            id: this.id,
            day: this.day,
            basePigeonId: this.basePigeonId,
            basePigeonSlug: this.basePigeonSlug,
            totalCaptures: this.totalCaptures,
        };
    }

    /**
     * Create a BasePigeonStats instance from a document
     */
    static fromDocument(doc: BasePigeonStatsDocument): BasePigeonStats {
        return new BasePigeonStats(doc);
    }

    /**
     * Generate a unique ID for a daily stats record
     */
    static generateId(day: string, basePigeonId: string): string {
        return `${day}_${basePigeonId}`;
    }

    /**
     * Format a date to the day string format (YYYY-MM-DD)
     */
    static formatDay(date: Date): string {
        return date.toISOString().split("T")[0];
    }
}

export interface BasePigeonStatsDocument {
    id: string;
    day: string;
    basePigeonId: string;
    basePigeonSlug: string;
    totalCaptures: number;
}

/**
 * Aggregated stats for a base pigeon across a date range
 */
export interface BasePigeonStatsAggregated {
    basePigeonId: string;
    basePigeonSlug: string;
    totalCaptures: number;
}
