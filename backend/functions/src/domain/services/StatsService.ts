import { PigeonRarity } from "../enums/PigeonRarity";
import { Stats } from "../value-objects/Stats";

export interface StatGenerationConfig {
    baseByRarity: Record<PigeonRarity, number>;
    meanAddition: number;
    stdDevAddition: number;
    minAddition: number;
    maxAddition: number;
    minStat: number;
    maxStat: number;
}

/* ---------- Default config ---------- */
const DEFAULT_CONFIG: StatGenerationConfig = {
    baseByRarity: {
        [PigeonRarity.COMMON]: 100,
        [PigeonRarity.RARE]: 130,
        [PigeonRarity.EPIC]: 160,
        [PigeonRarity.LEGENDARY]: 190,
    },
    meanAddition: 10,
    stdDevAddition: 5,
    minAddition: 0,
    maxAddition: 29,
    minStat: 25,
    maxStat: 100,
};

/* ---------- Service ---------- */
export class StatsService {
    private cfg: StatGenerationConfig;

    /**
     * Create a new StatsService.
     * Supply *any* subset of StatGenerationConfig to override defaults.
     */
    constructor(overrides: Partial<StatGenerationConfig> = {}) {
        // Merge two-level config (top level + baseByRarity map)
        const merged = {
            ...DEFAULT_CONFIG,
            ...overrides,
            baseByRarity: {
                ...DEFAULT_CONFIG.baseByRarity,
                ...(overrides.baseByRarity ?? {}),
            },
        };

        this._validateConfig(merged);

        this.cfg = merged;
    }

    private _validateConfig(cfg: StatGenerationConfig) {
        const guaranteed = 3 * cfg.minStat;
        for (const rarity of Object.values(PigeonRarity).filter((v) => typeof v === "number") as PigeonRarity[]) {
            const minPool = cfg.baseByRarity[rarity] + cfg.minAddition;
            if (minPool < guaranteed) {
                throw new Error(
                    `Invalid config: ${PigeonRarity[rarity]} min pool ${minPool} < required ${guaranteed}.` +
                        " Increase baseByRarity, raise minAddition, or lower minStat.",
                );
            }
        }
    }

    /** Public API -------------------------------------------------- */
    computeBaseStatsBasedOnRarity(rarity: PigeonRarity): Stats {
        const base = this.cfg.baseByRarity[rarity];
        const addition = this.rollAddition();
        const pool = base + addition;

        const [health, strength, agility] = this.splitIntoStats(pool);
        return { health, strength, agility };
    }

    /* ---------- Private helpers ---------- */

    /** Box–Muller normal deviate */
    private randomNormal(mu = 0, sigma = 1): number {
        const u1 = 1 - Math.random();
        const u2 = Math.random();
        const z0 = Math.sqrt(-2.0 * Math.log(u1)) * Math.cos(2.0 * Math.PI * u2);
        return z0 * sigma + mu;
    }

    /** Roll extra points with truncation-resample inside bounds */
    private rollAddition(): number {
        const { meanAddition, stdDevAddition, minAddition, maxAddition } = this.cfg;
        let val: number;
        do {
            val = Math.round(this.randomNormal(meanAddition, stdDevAddition));
        } while (val < minAddition || val > maxAddition);
        return val;
    }

    /**
     * Stick-breaking split into three stats,
     * honouring minStat / maxStat. Repeats until valid.
     */
    private splitIntoStats(pool: number): [number, number, number] {
        const { minStat, maxStat } = this.cfg;
        const guaranteed = 3 * minStat;

        const rem = pool - guaranteed;

        // eslint-disable-next-line no-constant-condition
        while (true) {
            const r1 = Math.random() * rem;
            const r2 = Math.random() * rem;
            const [a, b] = r1 < r2 ? [r1, r2] : [r2, r1];

            const stat1 = minStat + Math.round(a);
            const stat2 = minStat + Math.round(b - a);
            const stat3 = minStat + Math.round(rem - b);

            if (stat1 <= maxStat && stat2 <= maxStat && stat3 <= maxStat) {
                return [stat1, stat2, stat3];
            }
            // else retry
        }
    }
}

/* ---------- Example usage ---------- */
// const svc = new StatsService();                       // default knobs
// const rareStats = svc.computeBaseStatsBasedOnRarity(PigeonRarity.RARE);
// console.log('Rare pigeon stats', rareStats);
//
// const spicySvc = new StatsService({
//   meanAddition: 25,
//   stdDevAddition: 10,
//   baseByRarity: { [PigeonRarity.LEGENDARY]: 400 },
// });
// const legendaryStats = spicySvc.computeBaseStatsBasedOnRarity(PigeonRarity.LEGENDARY);
// console.log('Spicy legendary', legendaryStats);
