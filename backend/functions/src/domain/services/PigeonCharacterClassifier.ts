/**
 * Pigeon Character Classification Algorithm
 *
 * This algorithm classifies pigeons into ~50 game characters based on their phenotype analysis.
 * The classification is hierarchical, checking the most distinctive features first.
 */

export interface PigeonAnalysisResult {
    filename: string;
    looks_like_a_screenshot: number;
    is_bird: boolean;
    species: string;
    is_baby_pigeon: boolean;
    is_dead: boolean;
    distinctiveness: "common" | "unusual" | "rare";
    description: string;
    pigeon_traits: PigeonTraits | null;
}

export interface PigeonTraits {
    base_color: "blue" | "ash-red" | "brown" | "other";
    main_pattern: "bar" | "checker" | "t-check" | "barless" | "other";
    is_spread: boolean;
    spread_level: "none" | "partial" | "full";
    is_piebald: boolean;
    piebald_level: "none" | "intermediate" | "heavy" | "full_white";
    piebald_pattern: "none" | "white_patches" | "small_white_spots" | "mottled";
    piebald_intensity: {
        head: "none" | "light" | "intermediate" | "heavy" | "fully_white";
        neck: "none" | "light" | "intermediate" | "heavy" | "fully_white";
        body: "none" | "light" | "intermediate" | "heavy" | "fully_white";
        tail: "none" | "light" | "intermediate" | "heavy" | "fully_white";
    } | null;
    head_pattern: "helmet" | "baldhead" | "none";
    iridescence_level: "none" | "low" | "medium" | "high";
    wing_tip_color: "black" | "dark" | "grey" | "light" | "white";
    tail_color: "black" | "dark" | "light" | "white" | "mixed";
    face_pattern: "uniform_standard" | "mottled" | "weird_spots";
}

export enum PigeonCharacter {
    // Non-pigeon species (9 characters)
    MAGPIE = "MAGPIE",
    WOOD_PIGEON = "WOOD_PIGEON",
    TURTLEDOVE = "TURTLEDOVE",
    DUCK = "DUCK",
    CROW = "CROW",
    RAVEN = "RAVEN",
    SEAGULL = "SEAGULL",
    ROOSTER = "ROOSTER",
    OTHER_BIRD = "OTHER_BIRD",

    // Special cases (2 characters)
    BABY_PIGEON = "BABY_PIGEON",
    DEAD_PIGEON = "DEAD_PIGEON",

    // Basic blue pigeons (4 characters)
    BLUE_BAR = "BLUE_BAR",
    BLUE_CHECKER = "BLUE_CHECKER",
    BLUE_T_CHECK = "BLUE_T_CHECK",
    BLUE_BARLESS = "BLUE_BARLESS",

    // Brown pigeons (4 characters)
    BROWN_BAR = "BROWN_BAR",
    BROWN_CHECKER = "BROWN_CHECKER",
    BROWN_OTHER = "BROWN_OTHER",
    BROWN_BARLESS = "BROWN_BARLESS",

    // Ash-red pigeons (3 characters)
    ASH_RED_BAR = "ASH_RED_BAR",
    ASH_RED_CHECKER = "ASH_RED_CHECKER",
    ASH_RED_BARLESS = "ASH_RED_BARLESS",

    // Spread pigeons (dark/black) (4 characters)
    BLUE_SPREAD_BAR = "BLUE_SPREAD_BAR",
    BLUE_SPREAD_CHECKER = "BLUE_SPREAD_CHECKER",
    BLUE_SPREAD_T_CHECK = "BLUE_SPREAD_T_CHECK",
    BLUE_SPREAD_OTHER = "BLUE_SPREAD_OTHER",

    // Head pattern pigeons (2 characters)
    HELMET_PIGEON = "HELMET_PIGEON",
    BALDHEAD_PIGEON = "BALDHEAD_PIGEON",

    // Piebald pigeons (12 characters)
    BLUE_BAR_LIGHT_PIEBALD = "BLUE_BAR_LIGHT_PIEBALD",
    BLUE_BAR_HEAVY_PIEBALD = "BLUE_BAR_HEAVY_PIEBALD",
    BLUE_CHECKER_LIGHT_PIEBALD = "BLUE_CHECKER_LIGHT_PIEBALD",
    BLUE_CHECKER_HEAVY_PIEBALD = "BLUE_CHECKER_HEAVY_PIEBALD",
    BROWN_LIGHT_PIEBALD = "BROWN_LIGHT_PIEBALD",
    BROWN_HEAVY_PIEBALD = "BROWN_HEAVY_PIEBALD",
    BLUE_SPREAD_LIGHT_PIEBALD = "BLUE_SPREAD_LIGHT_PIEBALD",
    BLUE_SPREAD_HEAVY_PIEBALD = "BLUE_SPREAD_HEAVY_PIEBALD",
    MOTTLED_PIEBALD = "MOTTLED_PIEBALD",
    WHITE_PATCHES_PIEBALD = "WHITE_PATCHES_PIEBALD",
    SMALL_WHITE_SPOTS_PIEBALD = "SMALL_WHITE_SPOTS_PIEBALD",
    EXTREME_PIEBALD = "EXTREME_PIEBALD",

    // Rare/unusual combinations (10 characters)
    BLUE_OTHER_PATTERN = "BLUE_OTHER_PATTERN",
    WEIRD_FACE_SPOTS = "WEIRD_FACE_SPOTS",
    MOTTLED_FACE = "MOTTLED_FACE",
    HIGH_IRIDESCENCE = "HIGH_IRIDESCENCE",
    NO_IRIDESCENCE = "NO_IRIDESCENCE",
    WHITE_WING_TIPS = "WHITE_WING_TIPS",
    WHITE_TAIL = "WHITE_TAIL",
    MIXED_TAIL_COLOR = "MIXED_TAIL_COLOR",
    OTHER_BASE_COLOR = "OTHER_BASE_COLOR",
    RARE_DISTINCTIVENESS = "RARE_DISTINCTIVENESS",
}

export class PigeonCharacterClassifier {
    /**
     * Main classification function that determines the character based on phenotype analysis
     */
    static classifyPigeon(analysis: PigeonAnalysisResult): PigeonCharacter {
        // 1. Check if it's not a bird
        if (!analysis.is_bird) {
            return PigeonCharacter.OTHER_BIRD;
        }

        // 2. Check for non-pigeon species first
        if (analysis.species !== "columba_livia") {
            switch (analysis.species) {
                case "magpie":
                    return PigeonCharacter.MAGPIE;
                case "wood_pigeon":
                    return PigeonCharacter.WOOD_PIGEON;
                case "turtledove":
                    return PigeonCharacter.TURTLEDOVE;
                case "duck":
                    return PigeonCharacter.DUCK;
                case "crow":
                    return PigeonCharacter.CROW;
                case "raven":
                    return PigeonCharacter.RAVEN;
                case "seagull":
                case "gull":
                    return PigeonCharacter.SEAGULL;
                case "rooster":
                case "hen":
                    return PigeonCharacter.ROOSTER;
                default:
                    return PigeonCharacter.OTHER_BIRD;
            }
        }

        // 3. Check for special cases
        if (analysis.is_baby_pigeon) {
            return PigeonCharacter.BABY_PIGEON;
        }

        if (analysis.is_dead) {
            return PigeonCharacter.DEAD_PIGEON;
        }

        // 4. From here on, we're dealing with columba_livia
        const traits = analysis.pigeon_traits;
        if (!traits) {
            return PigeonCharacter.BLUE_BAR; // Default fallback
        }

        // 5. Check for distinctive head patterns first (very rare and distinctive)
        if (traits.head_pattern === "helmet") {
            return PigeonCharacter.HELMET_PIGEON;
        }
        if (traits.head_pattern === "baldhead") {
            return PigeonCharacter.BALDHEAD_PIGEON;
        }

        // 6. Check for rare distinctiveness (curly feathers, etc.)
        if (analysis.distinctiveness === "rare") {
            return PigeonCharacter.RARE_DISTINCTIVENESS;
        }

        // 7. Check for special face patterns
        if (traits.face_pattern === "weird_spots") {
            return PigeonCharacter.WEIRD_FACE_SPOTS;
        }
        if (traits.face_pattern === "mottled") {
            return PigeonCharacter.MOTTLED_FACE;
        }

        // 8. Check for extreme iridescence levels
        if (traits.iridescence_level === "high") {
            return PigeonCharacter.HIGH_IRIDESCENCE;
        }
        if (traits.iridescence_level === "none") {
            return PigeonCharacter.NO_IRIDESCENCE;
        }

        // 9. Check for unusual wing/tail colors
        if (traits.wing_tip_color === "white") {
            return PigeonCharacter.WHITE_WING_TIPS;
        }
        if (traits.tail_color === "white") {
            return PigeonCharacter.WHITE_TAIL;
        }
        if (traits.tail_color === "mixed") {
            return PigeonCharacter.MIXED_TAIL_COLOR;
        }

        // 10. Check for other base color
        if (traits.base_color === "other") {
            return PigeonCharacter.OTHER_BASE_COLOR;
        }

        // 11. Handle spread mutations (creates very dark pigeons)
        if (traits.is_spread && traits.spread_level === "full") {
            if (traits.is_piebald) {
                if (traits.piebald_level === "heavy") {
                    return PigeonCharacter.BLUE_SPREAD_HEAVY_PIEBALD;
                } else {
                    return PigeonCharacter.BLUE_SPREAD_LIGHT_PIEBALD;
                }
            }

            switch (traits.main_pattern) {
                case "bar":
                    return PigeonCharacter.BLUE_SPREAD_BAR;
                case "checker":
                    return PigeonCharacter.BLUE_SPREAD_CHECKER;
                case "t-check":
                    return PigeonCharacter.BLUE_SPREAD_T_CHECK;
                default:
                    return PigeonCharacter.BLUE_SPREAD_OTHER;
            }
        }

        // 12. Handle piebald patterns (white markings)
        if (traits.is_piebald && traits.piebald_level !== "none") {
            // Check for extreme piebald first
            if (
                traits.piebald_level === "full_white" ||
                (traits.piebald_intensity?.head === "fully_white" && traits.piebald_intensity?.body === "fully_white")
            ) {
                return PigeonCharacter.EXTREME_PIEBALD;
            }

            // Check piebald pattern type
            if (traits.piebald_pattern === "mottled") {
                return PigeonCharacter.MOTTLED_PIEBALD;
            }
            if (traits.piebald_pattern === "white_patches") {
                return PigeonCharacter.WHITE_PATCHES_PIEBALD;
            }
            if (traits.piebald_pattern === "small_white_spots") {
                return PigeonCharacter.SMALL_WHITE_SPOTS_PIEBALD;
            }

            // Base color + pattern + piebald level combinations
            if (traits.base_color === "brown") {
                return traits.piebald_level === "heavy"
                    ? PigeonCharacter.BROWN_HEAVY_PIEBALD
                    : PigeonCharacter.BROWN_LIGHT_PIEBALD;
            }

            if (traits.base_color === "blue") {
                if (traits.main_pattern === "bar") {
                    return traits.piebald_level === "heavy"
                        ? PigeonCharacter.BLUE_BAR_HEAVY_PIEBALD
                        : PigeonCharacter.BLUE_BAR_LIGHT_PIEBALD;
                }
                if (traits.main_pattern === "checker") {
                    return traits.piebald_level === "heavy"
                        ? PigeonCharacter.BLUE_CHECKER_HEAVY_PIEBALD
                        : PigeonCharacter.BLUE_CHECKER_LIGHT_PIEBALD;
                }
            }
        }

        // 13. Handle base color and pattern combinations (standard pigeons)
        switch (traits.base_color) {
            case "brown":
                switch (traits.main_pattern) {
                    case "bar":
                        return PigeonCharacter.BROWN_BAR;
                    case "checker":
                        return PigeonCharacter.BROWN_CHECKER;
                    case "barless":
                        return PigeonCharacter.BROWN_BARLESS;
                    default:
                        return PigeonCharacter.BROWN_OTHER;
                }

            case "ash-red":
                switch (traits.main_pattern) {
                    case "bar":
                        return PigeonCharacter.ASH_RED_BAR;
                    case "checker":
                        return PigeonCharacter.ASH_RED_CHECKER;
                    case "barless":
                        return PigeonCharacter.ASH_RED_BARLESS;
                    default:
                        return PigeonCharacter.ASH_RED_BAR; // Default to bar
                }

            case "blue":
            default:
                switch (traits.main_pattern) {
                    case "bar":
                        return PigeonCharacter.BLUE_BAR;
                    case "checker":
                        return PigeonCharacter.BLUE_CHECKER;
                    case "t-check":
                        return PigeonCharacter.BLUE_T_CHECK;
                    case "barless":
                        return PigeonCharacter.BLUE_BARLESS;
                    case "other":
                        return PigeonCharacter.BLUE_OTHER_PATTERN;
                    default:
                        return PigeonCharacter.BLUE_BAR; // Default fallback
                }
        }
    }

    /**
     * Get a human-readable description of the character
     */
    static getCharacterDescription(character: PigeonCharacter): string {
        const descriptions: Record<PigeonCharacter, string> = {
            [PigeonCharacter.MAGPIE]: "A striking black and white magpie",
            [PigeonCharacter.WOOD_PIGEON]: "A large wood pigeon with distinctive white neck patches",
            [PigeonCharacter.TURTLEDOVE]: "A gentle turtledove with soft coloring",
            [PigeonCharacter.DUCK]: "A waterfowl duck",
            [PigeonCharacter.CROW]: "A black crow",
            [PigeonCharacter.RAVEN]: "A large black raven",
            [PigeonCharacter.SEAGULL]: "A coastal seagull",
            [PigeonCharacter.ROOSTER]: "A domestic rooster or hen",
            [PigeonCharacter.OTHER_BIRD]: "An unidentified bird species",

            [PigeonCharacter.BABY_PIGEON]: "A young pigeon squab with developing plumage",
            [PigeonCharacter.DEAD_PIGEON]: "A deceased pigeon",

            [PigeonCharacter.BLUE_BAR]: "The classic city pigeon with blue-grey plumage and two black wing bars",
            [PigeonCharacter.BLUE_CHECKER]: "A blue pigeon with checkered wing pattern",
            [PigeonCharacter.BLUE_T_CHECK]: "A blue pigeon with dense checkering pattern",
            [PigeonCharacter.BLUE_BARLESS]: "A blue pigeon without wing bars",

            [PigeonCharacter.BROWN_BAR]: "A brown pigeon with wing bars",
            [PigeonCharacter.BROWN_CHECKER]: "A brown pigeon with checkered pattern",
            [PigeonCharacter.BROWN_OTHER]: "A brown pigeon with unusual pattern",
            [PigeonCharacter.BROWN_BARLESS]: "A brown pigeon without wing bars",

            [PigeonCharacter.ASH_RED_BAR]: "An ash-red pigeon with wing bars",
            [PigeonCharacter.ASH_RED_CHECKER]: "An ash-red pigeon with checkered pattern",
            [PigeonCharacter.ASH_RED_BARLESS]: "An ash-red pigeon without wing bars",

            [PigeonCharacter.BLUE_SPREAD_BAR]: "A very dark pigeon with spread mutation and bar pattern",
            [PigeonCharacter.BLUE_SPREAD_CHECKER]: "A very dark pigeon with spread mutation and checker pattern",
            [PigeonCharacter.BLUE_SPREAD_T_CHECK]: "A very dark pigeon with spread mutation and t-check pattern",
            [PigeonCharacter.BLUE_SPREAD_OTHER]: "A very dark pigeon with spread mutation",

            [PigeonCharacter.HELMET_PIGEON]: "A pigeon with distinctive white cap marking",
            [PigeonCharacter.BALDHEAD_PIGEON]: "A pigeon with entirely white head",

            [PigeonCharacter.BLUE_BAR_LIGHT_PIEBALD]: "A blue bar pigeon with light white markings",
            [PigeonCharacter.BLUE_BAR_HEAVY_PIEBALD]: "A blue bar pigeon with extensive white markings",
            [PigeonCharacter.BLUE_CHECKER_LIGHT_PIEBALD]: "A blue checker pigeon with light white markings",
            [PigeonCharacter.BLUE_CHECKER_HEAVY_PIEBALD]: "A blue checker pigeon with extensive white markings",
            [PigeonCharacter.BROWN_LIGHT_PIEBALD]: "A brown pigeon with light white markings",
            [PigeonCharacter.BROWN_HEAVY_PIEBALD]: "A brown pigeon with extensive white markings",
            [PigeonCharacter.BLUE_SPREAD_LIGHT_PIEBALD]: "A dark spread pigeon with light white markings",
            [PigeonCharacter.BLUE_SPREAD_HEAVY_PIEBALD]: "A dark spread pigeon with extensive white markings",
            [PigeonCharacter.MOTTLED_PIEBALD]: "A pigeon with mottled white and colored feathers",
            [PigeonCharacter.WHITE_PATCHES_PIEBALD]: "A pigeon with distinct white patches",
            [PigeonCharacter.SMALL_WHITE_SPOTS_PIEBALD]: "A pigeon with small white spots",
            [PigeonCharacter.EXTREME_PIEBALD]: "A pigeon with extreme white coloration",

            [PigeonCharacter.BLUE_OTHER_PATTERN]: "A blue pigeon with unusual wing pattern",
            [PigeonCharacter.WEIRD_FACE_SPOTS]: "A pigeon with unusual facial markings",
            [PigeonCharacter.MOTTLED_FACE]: "A pigeon with mottled facial coloring",
            [PigeonCharacter.HIGH_IRIDESCENCE]: "A pigeon with brilliant neck iridescence",
            [PigeonCharacter.NO_IRIDESCENCE]: "A pigeon with no neck iridescence",
            [PigeonCharacter.WHITE_WING_TIPS]: "A pigeon with white wing tips",
            [PigeonCharacter.WHITE_TAIL]: "A pigeon with white tail feathers",
            [PigeonCharacter.MIXED_TAIL_COLOR]: "A pigeon with mixed-colored tail",
            [PigeonCharacter.OTHER_BASE_COLOR]: "A pigeon with unusual base coloration",
            [PigeonCharacter.RARE_DISTINCTIVENESS]: "A pigeon with rare features like curly feathers",
        };

        return descriptions[character] || "Unknown pigeon character";
    }
}
