import { PigeonGang } from "../enums/PigeonGang";

export interface Coordinates {
    latitude: number;
    longitude: number;
}

export interface GeoLocationService {
    /**
     * Determines which gang a pigeon belongs to based on its capture coordinates and the date/time.
     * @param coordinates The latitude and longitude where the pigeon was captured
     * @param date Optional date to use for calculations (defaults to current date/time if not provided)
     * @returns The gang that the pigeon belongs to
     */
    determineGang(coordinates: Coordinates, date: Date): PigeonGang;
}
