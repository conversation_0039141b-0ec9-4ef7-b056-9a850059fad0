import { BasePigeonStats, BasePigeonStatsDocument, BasePigeonStatsAggregated } from "../entities/BasePigeonStats";

export interface BasePigeonStatsRepository {
    /**
     * Increment the capture count for a base pigeon on a specific day
     * Creates a new record if it doesn't exist, otherwise increments the existing count
     * @param day The day in YYYY-MM-DD format
     * @param basePigeonId The ID of the base pigeon
     * @param basePigeonSlug The slug of the base pigeon
     */
    incrementCapture(day: string, basePigeonId: string, basePigeonSlug: string): Promise<void>;

    /**
     * Get stats for a specific day and base pigeon
     * @param day The day in YYYY-MM-DD format
     * @param basePigeonId The ID of the base pigeon
     * @returns The stats record or null if not found
     */
    getByDayAndBasePigeon(day: string, basePigeonId: string): Promise<BasePigeonStats | null>;

    /**
     * Get aggregated stats for all base pigeons within a date range
     * If no date range is provided, returns stats for all time
     * @param startDate Optional start date (inclusive)
     * @param endDate Optional end date (inclusive)
     * @returns Array of aggregated stats grouped by base pigeon
     */
    getAggregatedStats(startDate?: Date, endDate?: Date): Promise<BasePigeonStatsAggregated[]>;

    create(stats: BasePigeonStatsDocument): Promise<void>;

    update(stats: BasePigeonStatsDocument): Promise<void>;
}
