import { AnalysisJob, AnalysisJobDocument } from "../entities/AnalysisJob";

export interface AnalysisJobRepository {
    /**
     * Create a new analysis job
     * @param job The analysis job to create
     */
    create(job: AnalysisJobDocument): Promise<void>;

    /**
     * Update an existing analysis job
     * @param job The analysis job to update
     */
    update(job: AnalysisJobDocument): Promise<void>;

    /**
     * Get an analysis job by its capture ID
     * @param captureId The capture ID to search for
     * @returns The analysis job or null if not found
     */
    getByCaptureId(captureId: string): Promise<AnalysisJob | null>;

    /**
     * Get all analysis jobs for a specific trainer
     * @param trainerId The trainer ID to search for
     * @param limit Optional limit for the number of results
     * @param offset Optional offset for pagination
     * @returns Array of analysis jobs
     */
    getByTrainerId(trainerId: string, limit?: number, offset?: number): Promise<AnalysisJob[]>;
}
