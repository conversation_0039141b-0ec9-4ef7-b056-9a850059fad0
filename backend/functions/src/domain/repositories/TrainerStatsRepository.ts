import { TrainerStats, TrainerStatsDocument } from "../entities/TrainerStats";
import { TrainerStatsOrderBy } from "../enums/TrainerStatsOrderBy";

export interface TrainerStatsRepository {
    getById(id: string): Promise<TrainerStats | null>;
    getByTrainerId(trainerId: string): Promise<TrainerStats | null>;
    update(trainerStats: TrainerStatsDocument): Promise<void>;
    create(trainerStats: TrainerStatsDocument): Promise<void>;
    getLeaderBoard(orderBy: TrainerStatsOrderBy, limit: number): Promise<TrainerStats[]>;
}
