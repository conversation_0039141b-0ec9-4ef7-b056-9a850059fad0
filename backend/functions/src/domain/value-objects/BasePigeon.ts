import { defaultAnalysis, PigeonAnalysis } from "./PigeonAnalysis";
import { PigeonSkin } from "./PigeonSkin";

export type BasePigeon = {
    id: string;
    name: {
        fr: string;
        en: string;
    };
    slug: string; // name in fr in underscore case
    story: {
        fr: string;
        en: string;
    };
    skin: PigeonSkin;
    typicalAnalysis: PigeonAnalysis;
};

export const defaultBasePigeon: BasePigeon = {
    id: "1cdc3de0-a368-4448-be9e-5a3564ef8e36",
    name: {
        fr: "<PERSON>'Indécis",
        en: "Speedy Gonzales",
    },
    slug: "pete_l_indecis",
    story: {
        fr: "Le plus rapide des pigeons",
        en: "The fastest pigeon in town",
    },
    skin: {
        smallUrl: "https://example.com/default_pigeon_thumbnail.png",
        originalUrl: "https://example.com/default_pigeon.png",
    },
    typicalAnalysis: defaultAnalysis,
};
