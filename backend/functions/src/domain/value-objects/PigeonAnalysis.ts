import { BirdType } from "../enums/BirdType";
import { PigeonDistinctiveness } from "../enums/PigeonDistinctiveness";

export type PigeonAnalysis = {
    isFake: number; // 0-10 scale where 0 means not fake, 10 means definitely fake
    // Basic characteristics
    isBird: boolean;
    isDead: boolean;
    isABabyPigeon: boolean;
    birdType: BirdType;

    // Classification fields
    distinctiveness: PigeonDistinctiveness; // How distinctive the pigeon's appearance is (COMMON, UNUSUAL, RARE)
    description: string; // Detailed description of the pigeon's plumage and patterns
};

// Create a default analysis
export const defaultAnalysis: PigeonAnalysis = {
    isFake: 0,
    isBird: true,
    isDead: false,
    isABabyPigeon: false,
    birdType: BirdType.PIGEON,
    distinctiveness: PigeonDistinctiveness.COMMON,
    description: `A standard city pigeon with dark grey head, iridescent green neck, light grey wings with two black bars, and a dark grey tail.
    The body shows the classic rock pigeon coloration with minimal variation or distinctive markings.`,
};

// Type for the keys used in the distance calculation
export type PigeonAnalysisKeyList = Array<
    keyof Pick<
        PigeonAnalysis,
        "isFake" | "isBird" | "isDead" | "isABabyPigeon" | "birdType" | "distinctiveness" | "description"
    >
>;
