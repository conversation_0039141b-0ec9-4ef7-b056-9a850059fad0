/**
 * Example usage of the Pigeon Character Classification Algorithm
 * 
 * This example shows how to integrate the character classifier
 * into the pigeon capture and analysis workflow.
 */

import { PigeonCharacterClassifier, PigeonCharacter, PigeonAnalysisResult } from "../domain/services/PigeonCharacterClassifier";

/**
 * Example: Classify a pigeon from analysis results
 */
function classifyPigeonExample() {
    // Example analysis result from OpenAI analysis
    const analysisResult: PigeonAnalysisResult = {
        filename: "example_pigeon.jpg",
        looks_like_a_screenshot: 3,
        is_bird: true,
        species: "columba_livia",
        is_baby_pigeon: false,
        is_dead: false,
        distinctiveness: "common",
        description: "This pigeon has a standard blue-grey plumage with two black wing bars, a dark head, and iridescent green on the neck. The pattern and coloration are typical for feral pigeons.",
        pigeon_traits: {
            base_color: "blue",
            main_pattern: "bar",
            is_spread: false,
            spread_level: "none",
            is_piebald: false,
            piebald_level: "none",
            piebald_pattern: "none",
            piebald_intensity: null,
            head_pattern: "none",
            iridescence_level: "medium",
            wing_tip_color: "black",
            tail_color: "black",
            face_pattern: "uniform_standard"
        }
    };

    // Classify the pigeon
    const character = PigeonCharacterClassifier.classifyPigeon(analysisResult);
    const description = PigeonCharacterClassifier.getCharacterDescription(character);

    console.log(`Pigeon classified as: ${character}`);
    console.log(`Description: ${description}`);
    
    return { character, description };
}

/**
 * Example: Batch classify multiple pigeons
 */
function batchClassifyExample(analysisResults: PigeonAnalysisResult[]) {
    const classifications = analysisResults.map(analysis => {
        const character = PigeonCharacterClassifier.classifyPigeon(analysis);
        const description = PigeonCharacterClassifier.getCharacterDescription(character);
        
        return {
            filename: analysis.filename,
            character,
            description,
            rarity: getCharacterRarity(character),
            originalAnalysis: analysis
        };
    });

    return classifications;
}

/**
 * Example: Get character rarity for game mechanics
 */
function getCharacterRarity(character: PigeonCharacter): "common" | "uncommon" | "rare" | "legendary" {
    // Define rarity based on character type
    const legendaryCharacters = [
        PigeonCharacter.RARE_DISTINCTIVENESS,
        PigeonCharacter.EXTREME_PIEBALD,
        PigeonCharacter.OTHER_BASE_COLOR
    ];

    const rareCharacters = [
        PigeonCharacter.HELMET_PIGEON,
        PigeonCharacter.BALDHEAD_PIGEON,
        PigeonCharacter.ASH_RED_BAR,
        PigeonCharacter.ASH_RED_CHECKER,
        PigeonCharacter.ASH_RED_BARLESS,
        PigeonCharacter.BABY_PIGEON,
        PigeonCharacter.MAGPIE,
        PigeonCharacter.WOOD_PIGEON,
        PigeonCharacter.TURTLEDOVE
    ];

    const uncommonCharacters = [
        PigeonCharacter.BLUE_SPREAD_BAR,
        PigeonCharacter.BLUE_SPREAD_CHECKER,
        PigeonCharacter.BLUE_SPREAD_T_CHECK,
        PigeonCharacter.BLUE_SPREAD_OTHER,
        PigeonCharacter.BROWN_BAR,
        PigeonCharacter.BROWN_CHECKER,
        PigeonCharacter.BROWN_OTHER,
        PigeonCharacter.BROWN_BARLESS,
        PigeonCharacter.WHITE_PATCHES_PIEBALD,
        PigeonCharacter.MOTTLED_PIEBALD,
        PigeonCharacter.HIGH_IRIDESCENCE,
        PigeonCharacter.NO_IRIDESCENCE,
        PigeonCharacter.WHITE_WING_TIPS,
        PigeonCharacter.WHITE_TAIL
    ];

    if (legendaryCharacters.includes(character)) {
        return "legendary";
    } else if (rareCharacters.includes(character)) {
        return "rare";
    } else if (uncommonCharacters.includes(character)) {
        return "uncommon";
    } else {
        return "common";
    }
}

/**
 * Example: Integration with game capture system
 */
class PigeonCaptureService {
    /**
     * Process a captured pigeon image and determine its character
     */
    async processCapturedPigeon(imagePath: string, analysisResult: PigeonAnalysisResult) {
        // 1. Classify the pigeon
        const character = PigeonCharacterClassifier.classifyPigeon(analysisResult);
        const description = PigeonCharacterClassifier.getCharacterDescription(character);
        const rarity = getCharacterRarity(character);

        // 2. Create pigeon data for the game
        const pigeonData = {
            id: this.generatePigeonId(),
            imagePath,
            character,
            characterDescription: description,
            rarity,
            captureDate: new Date(),
            location: this.extractLocationFromPath(imagePath),
            gang: this.determineGang(imagePath), // Based on location
            traits: analysisResult.pigeon_traits,
            distinctiveness: analysisResult.distinctiveness,
            originalDescription: analysisResult.description,
            stats: this.generateStats(character, rarity)
        };

        // 3. Save to database
        await this.savePigeonToDatabase(pigeonData);

        // 4. Return result for UI
        return {
            success: true,
            pigeon: pigeonData,
            isNewCharacter: await this.isNewCharacterForTrainer(character),
            characterUnlocked: character
        };
    }

    private generatePigeonId(): string {
        return `pigeon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    private extractLocationFromPath(imagePath: string): { lat: number; lng: number } {
        // Extract coordinates from file path as per game mechanics
        // This would be implemented based on your coordinate parsing logic
        return { lat: 0, lng: 0 };
    }

    private determineGang(imagePath: string): number {
        // Determine gang based on location as per game mechanics
        // This would use your gang grid system
        return 1;
    }

    private generateStats(character: PigeonCharacter, rarity: string): any {
        // Generate battle stats based on character and rarity
        const baseStats = {
            attack: Math.floor(Math.random() * 10) + 1,
            defense: Math.floor(Math.random() * 10) + 1,
            speed: Math.floor(Math.random() * 10) + 1,
            intelligence: Math.floor(Math.random() * 10) + 1,
            charisma: Math.floor(Math.random() * 10) + 1
        };

        // Boost stats based on rarity
        const rarityMultiplier = {
            common: 1.0,
            uncommon: 1.2,
            rare: 1.5,
            legendary: 2.0
        }[rarity] || 1.0;

        return Object.fromEntries(
            Object.entries(baseStats).map(([key, value]) => [
                key,
                Math.min(10, Math.floor(value * rarityMultiplier))
            ])
        );
    }

    private async savePigeonToDatabase(pigeonData: any): Promise<void> {
        // Save pigeon to Firestore
        console.log("Saving pigeon to database:", pigeonData.id);
    }

    private async isNewCharacterForTrainer(character: PigeonCharacter): Promise<boolean> {
        // Check if trainer has captured this character type before
        return Math.random() > 0.7; // 30% chance of new character for demo
    }
}

/**
 * Example usage
 */
export function runExamples() {
    console.log("=== Pigeon Character Classification Examples ===\n");

    // Example 1: Single pigeon classification
    console.log("1. Single Pigeon Classification:");
    const result = classifyPigeonExample();
    console.log(`Result: ${result.character} - ${result.description}\n`);

    // Example 2: Character rarity
    console.log("2. Character Rarity Examples:");
    const characters = [
        PigeonCharacter.BLUE_BAR,
        PigeonCharacter.HELMET_PIGEON,
        PigeonCharacter.RARE_DISTINCTIVENESS,
        PigeonCharacter.MAGPIE
    ];

    characters.forEach(character => {
        const rarity = getCharacterRarity(character);
        const description = PigeonCharacterClassifier.getCharacterDescription(character);
        console.log(`${character}: ${rarity} - ${description}`);
    });

    console.log("\n=== Examples completed ===");
}

// Run examples if called directly
if (require.main === module) {
    runExamples();
}
