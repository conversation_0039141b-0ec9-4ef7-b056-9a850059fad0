import { AdvancedGeoLocationService } from "../infrastructure/geolocation/AdvancedGeoLocationService";
import { PigeonGang } from "../domain/enums/PigeonGang";
import { Coordinates } from "../domain/services/GeoLocationService";

describe("AdvancedGeoLocationService", () => {
    let service: AdvancedGeoLocationService;

    beforeEach(() => {
        service = new AdvancedGeoLocationService();
    });

    describe("determineGang", () => {
        it("should assign gangs based on grid position", () => {
            // Test coordinates in different quarters of a grid
            // Using a specific grid square for predictable results
            // Each grid is 0.05 degrees, so we'll use coordinates within the (0,0) to (0.05,0.05) grid
            const testCoordinates: Array<[number, number, PigeonGang]> = [
                [0.01, 0.01, PigeonGang.MAISON_CONTI], // Bottom-left quarter (before rotation)
                [0.03, 0.01, PigeonGang.LE_SYNDICAT], // Bottom-right quarter (before rotation)
                [0.01, 0.03, <PERSON>eonG<PERSON>.MAISON_ROSSINI], // Top-left quarter (before rotation)
                [0.03, 0.03, PigeonGang.LES_CRUMBS], // Top-right quarter (before rotation)
            ];

            // Use a fixed date with hour 0 (no rotation)
            const testDate = new Date(2025, 0, 1, 0, 0, 0, 0);

            // For each test coordinate
            for (const [longitude, latitude, expectedGang] of testCoordinates) {
                const coordinates: Coordinates = { latitude, longitude };
                const gang = service.determineGang(coordinates, testDate);

                // Verify the gang matches the expected gang
                expect(gang).toBe(expectedGang);
            }
        });

        it("should rotate gangs based on hour of day", () => {
            // Test a specific coordinate in the bottom-left quarter (Gang THREE at hour 0)
            const coordinates: Coordinates = { latitude: 0.01, longitude: 0.01 };

            // Test for each hour in the 4-hour rotation cycle
            const expectedGangs = [
                PigeonGang.MAISON_CONTI, // Hour 0
                PigeonGang.LE_SYNDICAT, // Hour 1
                PigeonGang.MAISON_ROSSINI, // Hour 2
                PigeonGang.LES_CRUMBS, // Hour 3
            ];

            for (let hour = 0; hour < 4; hour++) {
                const testDate = new Date(2025, 0, 1, hour, 0, 0, 0);
                const gang = service.determineGang(coordinates, testDate);

                expect(gang).toBe(expectedGangs[hour]);
            }
        });

        it("should handle dates before the cycle start date", () => {
            // Test with a date before the cycle start date
            const beforeCycleDate = new Date(2024, 11, 31, 23, 0, 0, 0); // December 31, 2024, 23:00

            // Use a coordinate that would be in Gang THREE's territory at hour 0
            const coordinates: Coordinates = { latitude: 0.01, longitude: 0.01 };

            // The service should handle this gracefully and still return a valid gang
            const gang = service.determineGang(coordinates, beforeCycleDate);

            // Verify the gang is valid
            expect(Object.values(PigeonGang)).toContain(gang);
        });

        it("should handle dates far in the future", () => {
            // Test with a date far in the future
            const futureDate = new Date(2030, 0, 1, 0, 0, 0, 0); // January 1, 2030

            // Use a coordinate that would be in Gang THREE's territory at hour 0
            const coordinates: Coordinates = { latitude: 0.01, longitude: 0.01 };

            // The service should handle this gracefully and still return a valid gang
            const gang = service.determineGang(coordinates, futureDate);

            // Verify the gang is valid
            expect(Object.values(PigeonGang)).toContain(gang);
        });

        it("should place Gang FIVE in the bottom-left square at the 35th % 64 hour", () => {
            // At the 35th hour of the cycle, Gang FIVE should be in the 1st square (bottom-left)
            // of the 8x8 grid (subGridX = 0, subGridY = 0)

            // Calculate the date for the 35th hour since cycle start
            const hoursSinceCycleStart = 35 + 64;
            const date = new Date(2025, 0, 1, 0, 0, 0, 0); // Cycle start date
            date.setHours(date.getHours() + hoursSinceCycleStart);

            // Create coordinates very close to 0,0 (bottom-left of the grid)
            const coordinates: Coordinates = { latitude: 0.0001, longitude: 0.0001 };

            // Determine the gang at these coordinates
            const gang = service.determineGang(coordinates, date);

            // Verify that it's Gang FIVE
            expect(gang).toBe(PigeonGang.OBNI);

            // Also verify that a slight offset from this position is not Gang FIVE
            const offsetCoordinates: Coordinates = { latitude: 0.01, longitude: 0.01 };
            const offsetGang = service.determineGang(offsetCoordinates, date);
            expect(offsetGang).not.toBe(PigeonGang.OBNI);
        });
        it("should place Gang FIVE in the top-right square at the 32nd % 64 hour", () => {
            // At the 35th hour of the cycle, Gang FIVE should be in the 1st square (bottom-left)
            // of the 8x8 grid (subGridX = 0, subGridY = 0)

            // Calculate the date for the 35th hour since cycle start
            const hoursSinceCycleStart = 32 + 64;
            const date = new Date(2025, 0, 1, 0, 0, 0, 0); // Cycle start date
            date.setHours(date.getHours() + hoursSinceCycleStart);

            // Create coordinates very close to 0,0 (bottom-left of the grid)
            const coordinates: Coordinates = { latitude: 0.0499, longitude: 0.0499 };

            // Determine the gang at these coordinates
            const gang = service.determineGang(coordinates, date);

            // Verify that it's Gang FIVE
            expect(gang).toBe(PigeonGang.OBNI);

            // Also verify that a slight offset from this position is not Gang FIVE
            const offsetCoordinates: Coordinates = { latitude: 0.0401, longitude: 0.0402 };
            const offsetGang = service.determineGang(offsetCoordinates, date);
            expect(offsetGang).not.toBe(PigeonGang.OBNI);
        });

        it("should handle edge cases in grid coordinates", () => {
            // Test with coordinates at the edge of a grid square
            const edgeCoordinates: Array<[number, number]> = [
                [0.05, 0.025], // Right edge of grid
                [0.025, 0.05], // Top edge of grid
                [0.0, 0.025], // Left edge of grid
                [0.025, 0.0], // Bottom edge of grid
                [0.05, 0.05], // Corner of grid
            ];

            const testDate = new Date(2025, 0, 1, 0, 0, 0, 0);

            for (const [longitude, latitude] of edgeCoordinates) {
                const coordinates: Coordinates = { latitude, longitude };
                const gang = service.determineGang(coordinates, testDate);

                // Verify the gang is valid
                expect(Object.values(PigeonGang)).toContain(gang);
            }
        });

        it("should handle negative coordinates properly", () => {
            const negativeCoordinates: Array<[number, number]> = [
                [-0.05, -0.025], // Right edge of grid
                [-0.025, -0.05], // Top edge of grid
                [-0.0, -0.025], // Left edge of grid
                [-0.025, -0.0], // Bottom edge of grid
            ];
            const testDate = new Date(2025, 0, 1, 0, 0, 0, 0);
            for (const [longitude, latitude] of negativeCoordinates) {
                const coordinates: Coordinates = { latitude, longitude };
                const gang = service.determineGang(coordinates, testDate);

                // Verify the gang is valid
                expect(Object.values(PigeonGang)).toContain(gang);
            }
        });
    });
});
