import { BattleUseCase, Position, PigeonInit, TrainerInit } from "../use-cases/BattleUseCaseV2";
import { PigeonGang } from "../domain/enums/PigeonGang";
import { Stats } from "../domain/value-objects/Stats";

describe("BattleUseCaseV2", () => {
    // Helper function to create balanced pigeon stats
    const createPigeonStats = (health: number, strength: number, agility: number): Stats => ({
        health,
        strength,
        agility,
    });

    // Helper function to create a pigeon with given parameters
    const createPigeon = (id: string, gang: PigeonGang, position: Position, baseStats: Stats): PigeonInit => ({
        id,
        gang,
        position,
        baseStats,
    });

    // Create balanced trainer A with diverse gangs and stats
    const createTrainerA = (): TrainerInit => ({
        id: "trainer-VIC",
        dignityPoints: 100,
        pigeons: [
            // Attack Line (LEFT, MID, RIGHT)
            createPigeon("VIC-attack-left", PigeonGang.MAISON_ROSSINI, Position.LEFT, createPigeonStats(80, 70, 25)), // High health, good strength, low agility
            createPigeon("VIC-attack-mid", PigeonGang.LES_CRUMBS, Position.MID, createPigeonStats(60, 85, 30)), // Balanced, high strength
            createPigeon("VIC-attack-right", PigeonGang.MAISON_CONTI, Position.RIGHT, createPigeonStats(70, 75, 35)), // Balanced stats, provides gang transfer

            // Defense Line (LEFT, MID, RIGHT)
            createPigeon("VIC-defense-left", PigeonGang.LE_SYNDICAT, Position.LEFT, createPigeonStats(90, 60, 20)), // Tank with high health
            createPigeon("VIC-defense-mid", PigeonGang.OBNI, Position.MID, createPigeonStats(75, 65, 40)), // Balanced with good agility
            createPigeon("VIC-defense-right", PigeonGang.MAISON_ROSSINI, Position.RIGHT, createPigeonStats(85, 70, 25)), // Another Rossini for resurrection synergy
        ],
    });

    // Create balanced trainer B with different gang composition
    const createTrainerB = (): TrainerInit => ({
        id: "trainer-SAMSAM",
        dignityPoints: 100,
        pigeons: [
            // Attack Line (LEFT, MID, RIGHT)
            createPigeon("SAMSAM-attack-left", PigeonGang.LES_CRUMBS, Position.LEFT, createPigeonStats(65, 80, 35)), // Good damage dealer with crumb splash potential
            createPigeon("SAMSAM-attack-mid", PigeonGang.OBNI, Position.MID, createPigeonStats(70, 75, 30)), // OBNI for stat boost to others
            createPigeon("SAMSAM-attack-right", PigeonGang.LE_SYNDICAT, Position.RIGHT, createPigeonStats(75, 70, 25)), // Provides gang diversity for healing

            // Defense Line (LEFT, MID, RIGHT)
            createPigeon("SAMSAM-defense-left", PigeonGang.MAISON_CONTI, Position.LEFT, createPigeonStats(80, 65, 45)), // High agility for initiative and dodge
            createPigeon("SAMSAM-defense-mid", PigeonGang.LES_CRUMBS, Position.MID, createPigeonStats(85, 60, 20)), // Another Crumbs for synergy
            createPigeon("SAMSAM-defense-right", PigeonGang.OBNI, Position.RIGHT, createPigeonStats(75, 70, 35)), // Another OBNI for more stat boosts
        ],
    });

    describe("Full Battle Simulation", () => {
        it.only("should run a complete battle between two balanced trainers", () => {
            // Arrange
            const trainerA = createTrainerA();
            const trainerB = createTrainerB();
            const battle = new BattleUseCase(trainerA, trainerB);

            // Act
            const result = battle.simulate();

            // Assert
            expect(result).toBeDefined();
            expect(result.winner).toBeDefined();
            expect(result.winner).toMatch(/^trainer-(VIC|SAMSAM)$/);
            expect(result.turns).toBeDefined();
            expect(result.turns.length).toBeGreaterThan(0);
            expect(result.turns.length).toBeLessThan(250); // Safety net check

            // Verify battle structure
            expect(result.turns[0].turn).toBe(1);
            expect(result.turns[0].phases).toBeDefined();
            expect(result.turns[0].phases.length).toBeGreaterThan(0);

            // Log battle summary for debugging
            console.log(`Battle completed in ${result.turns.length} turns`);
            console.log(`Winner: ${result.winner}`);
            console.log(`First turn actions: ${result.turns[0].phases.length}`);
            console.log(`Last turn actions: ${result.turns[result.turns.length - 1].phases.length}`);
            result.turns.forEach((turn) => {
                console.log(`\nTURN ${turn.turn}`);
                turn.phases.forEach((p) =>
                    console.log(`  • ${p.actor.padEnd(6)} ${p.pigeonId.padEnd(5)} → ${p.action} \n\n ${p.snapshot}`),
                );
            });
        });

        it("should properly handle gang abilities during battle", () => {
            // Arrange
            const trainerA = createTrainerA();
            const trainerB = createTrainerB();
            const battle = new BattleUseCase(trainerA, trainerB);

            // Act
            const result = battle.simulate();

            // Assert - Check for gang-specific actions in the battle log
            const allActions = result.turns.flatMap((turn) => turn.phases.map((phase) => phase.action));

            // Should have various types of actions
            const hasAttackActions = allActions.some((action) => action.includes("hits"));
            const hasTrainerDamage = allActions.some((action) => action.includes("strikes trainer"));

            expect(hasAttackActions).toBe(true);
            expect(hasTrainerDamage).toBe(true);

            // Check for potential gang abilities (these may or may not trigger due to RNG)
            const hasCrumbSplash = allActions.some((action) => action.includes("crumb-splash"));
            const hasResurrection = allActions.some((action) => action.includes("resurrects"));
            const hasHealing = allActions.some((action) => action.includes("heals"));
            const hasTransfer = allActions.some((action) => action.includes("transfers") && action.includes("dignity"));

            console.log(
                `Gang abilities observed: Crumb Splash: ${hasCrumbSplash}, Resurrection: ${hasResurrection}, Healing: ${hasHealing}, Transfer: ${hasTransfer}`,
            );

            // We expect at least dignity transfer to happen (guaranteed on pigeon death)
            expect(hasTransfer).toBe(true);
        });

        it("should respect positional bonuses and gang synergies", () => {
            // Arrange - Create trainers with specific gang compositions to test synergies
            const rossiniHeavyTrainer: TrainerInit = {
                id: "rossini-trainer",
                dignityPoints: 100,
                pigeons: [
                    createPigeon("r1", PigeonGang.MAISON_ROSSINI, Position.LEFT, createPigeonStats(60, 60, 20)),
                    createPigeon("r2", PigeonGang.MAISON_ROSSINI, Position.MID, createPigeonStats(60, 60, 20)),
                    createPigeon("r3", PigeonGang.MAISON_ROSSINI, Position.RIGHT, createPigeonStats(60, 60, 20)),
                    createPigeon("r4", PigeonGang.MAISON_ROSSINI, Position.LEFT, createPigeonStats(80, 50, 15)),
                    createPigeon("r5", PigeonGang.MAISON_ROSSINI, Position.MID, createPigeonStats(80, 50, 15)),
                    createPigeon("r6", PigeonGang.MAISON_ROSSINI, Position.RIGHT, createPigeonStats(80, 50, 15)),
                ],
            };

            const diverseTrainer: TrainerInit = {
                id: "diverse-trainer",
                dignityPoints: 100,
                pigeons: [
                    createPigeon("d1", PigeonGang.LES_CRUMBS, Position.LEFT, createPigeonStats(60, 60, 20)),
                    createPigeon("d2", PigeonGang.MAISON_CONTI, Position.MID, createPigeonStats(60, 60, 20)),
                    createPigeon("d3", PigeonGang.LE_SYNDICAT, Position.RIGHT, createPigeonStats(60, 60, 20)),
                    createPigeon("d4", PigeonGang.OBNI, Position.LEFT, createPigeonStats(80, 50, 15)),
                    createPigeon("d5", PigeonGang.MAISON_ROSSINI, Position.MID, createPigeonStats(80, 50, 15)),
                    createPigeon("d6", PigeonGang.LES_CRUMBS, Position.RIGHT, createPigeonStats(80, 50, 15)),
                ],
            };

            const battle = new BattleUseCase(rossiniHeavyTrainer, diverseTrainer);

            // Act
            const result = battle.simulate();

            // Assert
            expect(result).toBeDefined();
            expect(result.winner).toBeDefined();

            // The Rossini-heavy trainer should have more resurrection attempts
            const allActions = result.turns.flatMap((turn) => turn.phases.map((phase) => phase.action));
            const resurrections = allActions.filter((action) => action.includes("resurrects"));

            console.log(`Resurrections observed: ${resurrections.length}`);
            console.log(`Battle length: ${result.turns.length} turns`);

            // In a battle with 6 Rossini pigeons, we should see some resurrection attempts
            // (though they may not always succeed due to RNG)
            expect(result.turns.length).toBeGreaterThan(0);
        });
    });

    describe("Battle Mechanics", () => {
        it("should end when one trainer's dignity reaches 0 or below", () => {
            // Arrange
            const trainerA = createTrainerA();
            const trainerB = createTrainerB();
            const battle = new BattleUseCase(trainerA, trainerB);

            // Act
            const result = battle.simulate();

            // Assert
            expect(result.winner).toBeDefined();
            expect(result.turns.length).toBeGreaterThan(0);

            // Battle should end with a clear winner
            expect(["trainer-VIC", "trainer-SAMSAM"]).toContain(result.winner);
        });

        it("should have proper turn structure with phases", () => {
            // Arrange
            const trainerA = createTrainerA();
            const trainerB = createTrainerB();
            const battle = new BattleUseCase(trainerA, trainerB);

            // Act
            const result = battle.simulate();

            // Assert
            result.turns.forEach((turn, index) => {
                expect(turn.turn).toBe(index + 1);
                expect(turn.phases).toBeDefined();
                expect(Array.isArray(turn.phases)).toBe(true);

                // Each phase should have proper structure
                turn.phases.forEach((phase) => {
                    expect(phase.actor).toBeDefined();
                    expect(phase.pigeonId).toBeDefined();
                    expect(phase.action).toBeDefined();
                    expect(typeof phase.action).toBe("string");
                });
            });
        });

        it("should handle extreme gang compositions correctly", () => {
            // Arrange - Create a trainer with all the same gang vs diverse trainer
            const allCrumbsTrainer: TrainerInit = {
                id: "all-crumbs",
                dignityPoints: 100,
                pigeons: [
                    createPigeon("c1", PigeonGang.LES_CRUMBS, Position.LEFT, createPigeonStats(70, 80, 25)),
                    createPigeon("c2", PigeonGang.LES_CRUMBS, Position.MID, createPigeonStats(70, 80, 25)),
                    createPigeon("c3", PigeonGang.LES_CRUMBS, Position.RIGHT, createPigeonStats(70, 80, 25)),
                    createPigeon("c4", PigeonGang.LES_CRUMBS, Position.LEFT, createPigeonStats(90, 60, 15)),
                    createPigeon("c5", PigeonGang.LES_CRUMBS, Position.MID, createPigeonStats(90, 60, 15)),
                    createPigeon("c6", PigeonGang.LES_CRUMBS, Position.RIGHT, createPigeonStats(90, 60, 15)),
                ],
            };

            const diverseTrainer = createTrainerB();
            const battle = new BattleUseCase(allCrumbsTrainer, diverseTrainer);

            // Act
            const result = battle.simulate();

            // Assert
            expect(result).toBeDefined();
            expect(result.winner).toBeDefined();

            // Check for crumb splash effects with high gang count
            const allActions = result.turns.flatMap((turn) => turn.phases.map((phase) => phase.action));
            const crumbSplashes = allActions.filter((action) => action.includes("crumb-splash"));

            console.log(`All-Crumbs battle: ${crumbSplashes.length} crumb splashes in ${result.turns.length} turns`);

            // With 6 Crumbs pigeons, we should see some splash damage
            expect(result.turns.length).toBeGreaterThan(0);
        });
    });
});
