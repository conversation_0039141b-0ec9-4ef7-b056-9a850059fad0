import { GetLeaderBoardUseCase } from "../use-cases/GetLeaderBoardUseCase";
import { TrainerStatsRepository } from "../domain/repositories/TrainerStatsRepository";
import { TrainerStats, TrainerStatsDocument } from "../domain/entities/TrainerStats";
import { TrainerStatsOrderBy } from "../domain/enums/TrainerStatsOrderBy";
import { TrainerRepository } from "../domain/repositories/TrainerRepository";
import { Trainer, TrainerDocument } from "../domain/entities/Trainer";

class FakeTrainerStatsRepository implements TrainerStatsRepository {
    getById(id: string): Promise<TrainerStats | null> {
        throw new Error("Method not implemented.");
    }
    public trainerStats: { [id: string]: TrainerStats } = {};

    async getLeaderBoard(orderBy: TrainerStatsOrderBy, limit: number): Promise<TrainerStats[]> {
        const stats = Object.values(this.trainerStats);
        switch (orderBy) {
            case TrainerStatsOrderBy.TOTAL_CATCHER_POINTS:
                stats.sort((a, b) => b.totalCatcherPoints - a.totalCatcherPoints);
                break;
            case TrainerStatsOrderBy.TOTAL_PIGEON_COUNT:
                stats.sort((a, b) => b.pigeonCount - a.pigeonCount);
                break;
            case TrainerStatsOrderBy.DISTINCT_PIGEON_COUNT:
                stats.sort((a, b) => b.distinctPigeonCount - a.distinctPigeonCount);
                break;
            case TrainerStatsOrderBy.BATTLES_WON:
                stats.sort((a, b) => b.battlesWon - a.battlesWon);
                break;
            case TrainerStatsOrderBy.ELO:
                stats.sort((a, b) => b.elo - a.elo);
                break;
        }
        return stats.slice(0, limit);
    }

    async getByTrainerId(trainerId: string): Promise<TrainerStats | null> {
        return this.trainerStats[trainerId] || null;
    }

    async update(trainerStats: TrainerStatsDocument): Promise<void> {
        this.trainerStats[trainerStats.id] = new TrainerStats(trainerStats);
    }

    async create(trainerStats: TrainerStatsDocument): Promise<void> {
        this.trainerStats[trainerStats.id] = new TrainerStats(trainerStats);
    }
}

class FakeTrainerRepository implements TrainerRepository {
    public trainers: { [id: string]: Trainer } = {};

    async getById(id: string): Promise<Trainer | null> {
        return this.trainers[id] || null;
    }

    async getByIds(ids: string[]): Promise<Trainer[]> {
        return ids.map((id) => this.trainers[id]).filter(Boolean) as Trainer[];
    }

    async update(trainer: TrainerDocument): Promise<void> {
        this.trainers[trainer.id] = new Trainer(trainer);
    }

    async create(trainer: TrainerDocument): Promise<void> {
        this.trainers[trainer.id] = new Trainer(trainer);
    }
}

describe("GetLeaderBoardUseCase", () => {
    const fakeTrainerStatsRepo = new FakeTrainerStatsRepository();
    const fakeTrainerRepo = new FakeTrainerRepository();
    const getLeaderBoardUseCase = new GetLeaderBoardUseCase(fakeTrainerStatsRepo, fakeTrainerRepo);

    beforeEach(() => {
        // Clear repositories before each test
        fakeTrainerStatsRepo.trainerStats = {};
    });

    it("should return the leader board ordered by total catcher points", async () => {
        // Add some trainer stats for testing
        const trainerStats1 = new TrainerStats({
            id: "stats1",
            trainerId: "trainer1",
            pigeonCount: 10,
            distinctPigeonCount: 5,
            basePigeonList: [],
            legendaryCount: 1,
            epicCount: 2,
            rareCount: 3,
            commonCount: 4,
            totalCatcherPoints: 100,
            battlesWon: 5,
            battlesLost: 3,
            elo: 1200,
        });

        const trainerStats2 = new TrainerStats({
            id: "stats2",
            trainerId: "trainer2",
            pigeonCount: 8,
            distinctPigeonCount: 4,
            basePigeonList: [],
            legendaryCount: 0,
            epicCount: 1,
            rareCount: 2,
            commonCount: 3,
            totalCatcherPoints: 80,
            battlesWon: 4,
            battlesLost: 2,
            elo: 1000,
        });

        const trainerStats3 = new TrainerStats({
            id: "stats3",
            trainerId: "trainer3",
            pigeonCount: 12,
            distinctPigeonCount: 6,
            basePigeonList: [],
            legendaryCount: 2,
            epicCount: 3,
            rareCount: 4,
            commonCount: 5,
            totalCatcherPoints: 120,
            battlesWon: 6,
            battlesLost: 4,
            elo: 1400,
        });

        fakeTrainerRepo.trainers = {
            trainer1: new Trainer({
                id: "trainer1",
                username: "trainer1",
                pigeonBalls: 3,
                dignityPoints: 100,
                decks: [],
                currentDeckId: null,
            }),
            trainer2: new Trainer({
                id: "trainer2",
                username: "trainer2",
                pigeonBalls: 3,
                dignityPoints: 100,
                decks: [],
                currentDeckId: null,
            }),
            trainer3: new Trainer({
                id: "trainer3",
                username: "trainer3",
                pigeonBalls: 3,
                dignityPoints: 100,
                decks: [],
                currentDeckId: null,
            }),
        };

        fakeTrainerStatsRepo.trainerStats = {
            stats1: trainerStats1,
            stats2: trainerStats2,
            stats3: trainerStats3,
        };

        const result = await getLeaderBoardUseCase.execute(TrainerStatsOrderBy.TOTAL_CATCHER_POINTS, 100);

        // Should return the stats in the correct order
        expect(result.leaderBoard.map((s) => s.trainerId)).toEqual(["trainer3", "trainer1", "trainer2"]);

        const result2 = await getLeaderBoardUseCase.execute(TrainerStatsOrderBy.TOTAL_PIGEON_COUNT, 100);
        expect(result2.leaderBoard.map((s) => s.trainerId)).toEqual(["trainer3", "trainer1", "trainer2"]);

        const result3 = await getLeaderBoardUseCase.execute(TrainerStatsOrderBy.DISTINCT_PIGEON_COUNT, 100);
        expect(result3.leaderBoard.map((s) => s.trainerId)).toEqual(["trainer3", "trainer1", "trainer2"]);

        const result4 = await getLeaderBoardUseCase.execute(TrainerStatsOrderBy.BATTLES_WON, 100);
        expect(result4.leaderBoard.map((s) => s.trainerId)).toEqual(["trainer3", "trainer1", "trainer2"]);
    });
});
