import assert from "node:assert";
import { AnalysisJob } from "../domain/entities/AnalysisJob";
import { defaultPigeonWithBasePigeon } from "../domain/entities/Pigeon";
import { AnalysisJobErrorCode } from "../domain/enums/AnalysisJobErrorCode";
import { AnalysisJobStatus } from "../domain/enums/AnalysisJobStatus";
import { AnalysisJobRepository } from "../domain/repositories/AnalysisJobRepository";
import { GetAnalysisJobStatusUseCase } from "../use-cases/GetAnalysisJobStatusUseCase";

// Mock repository
class MockAnalysisJobRepository implements AnalysisJobRepository {
    private jobs: Map<string, AnalysisJob> = new Map();

    async create(job: any): Promise<void> {
        this.jobs.set(job.captureId, AnalysisJob.fromDocument(job));
    }

    async update(job: any): Promise<void> {
        this.jobs.set(job.captureId, AnalysisJob.fromDocument(job));
    }

    async getByCaptureId(captureId: string): Promise<AnalysisJob | null> {
        return this.jobs.get(captureId) || null;
    }

    async getByTrainerId(trainerId: string, limit?: number, offset?: number): Promise<AnalysisJob[]> {
        return Array.from(this.jobs.values()).filter((job) => job.trainerId === trainerId);
    }

    // Helper method for testing
    addJob(job: AnalysisJob): void {
        this.jobs.set(job.captureId, job);
    }

    clear(): void {
        this.jobs.clear();
    }
}

describe("GetAnalysisJobStatusUseCase", () => {
    let useCase: GetAnalysisJobStatusUseCase;
    let mockAnalysisJobRepo: MockAnalysisJobRepository;

    const mockDate = new Date("2025-01-01T00:00:00Z");

    beforeEach(() => {
        mockAnalysisJobRepo = new MockAnalysisJobRepository();
        useCase = new GetAnalysisJobStatusUseCase(mockAnalysisJobRepo);
    });

    afterEach(() => {
        mockAnalysisJobRepo.clear();
    });

    describe("execute", () => {
        it("should return analysis job when it exists", async () => {
            // Arrange
            const job = new AnalysisJob({
                captureId: "test-capture-id",
                trainerId: "test-trainer-id",
                storageFilePath: "shots/test-trainer-id/test-capture-id/40.7128_-74.0060_test.jpg",
                status: AnalysisJobStatus.PENDING,
                errorCode: null,
                errorMessage: null,
                createdAt: mockDate,
                updatedAt: mockDate,
                pigeon: null,
            });
            mockAnalysisJobRepo.addJob(job);

            // Act
            const result = await useCase.execute("test-capture-id");

            // Assert
            expect(result).toBeTruthy();
            assert(result);
            expect(result.captureId).toBe("test-capture-id");
            expect(result.trainerId).toBe("test-trainer-id");
            expect(result.status).toBe(AnalysisJobStatus.PENDING);
        });

        it("should return finished job with pigeon ID", async () => {
            // Arrange
            const job = new AnalysisJob({
                captureId: "test-capture-id",
                trainerId: "test-trainer-id",
                storageFilePath: "shots/test-trainer-id/test-capture-id/40.7128_-74.0060_test.jpg",
                status: AnalysisJobStatus.FINISHED,
                errorCode: null,
                errorMessage: null,
                createdAt: mockDate,
                updatedAt: mockDate,
                pigeon: defaultPigeonWithBasePigeon,
            });
            mockAnalysisJobRepo.addJob(job);

            // Act
            const result = await useCase.execute("test-capture-id");

            // Assert
            expect(result).toBeTruthy();
            assert(result);
            expect(result.status).toBe(AnalysisJobStatus.FINISHED);
            expect(result.pigeon).toBe(defaultPigeonWithBasePigeon);
            expect(result.errorCode).toBeNull();
            expect(result.errorMessage).toBeNull();
        });

        it("should return error job with error details", async () => {
            // Arrange
            const job = new AnalysisJob({
                captureId: "test-capture-id",
                trainerId: "test-trainer-id",
                storageFilePath: "shots/test-trainer-id/test-capture-id/40.7128_-74.0060_test.jpg",
                status: AnalysisJobStatus.ERROR,
                errorCode: AnalysisJobErrorCode.NOT_A_BIRD,
                errorMessage: "Image does not show a bird",
                createdAt: mockDate,
                updatedAt: mockDate,
                pigeon: null,
            });
            mockAnalysisJobRepo.addJob(job);

            // Act
            const result = await useCase.execute("test-capture-id");

            // Assert
            expect(result).toBeTruthy();
            assert(result);
            expect(result.status).toBe(AnalysisJobStatus.ERROR);
            expect(result.errorCode).toBe(AnalysisJobErrorCode.NOT_A_BIRD);
            expect(result.errorMessage).toBe("Image does not show a bird");
            expect(result.pigeon).toBeNull();
        });

        it("should return null when job does not exist", async () => {
            // Act
            const result = await useCase.execute("non-existent-capture-id");

            // Assert
            expect(result).toBeNull();
        });
    });
});
