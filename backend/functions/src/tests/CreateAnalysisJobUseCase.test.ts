import { <PERSON><PERSON>ob } from "../domain/entities/AnalysisJob";
import { Trainer } from "../domain/entities/Trainer";
import { AnalysisJobStatus } from "../domain/enums/AnalysisJobStatus";
import { AnalysisJobRepository } from "../domain/repositories/AnalysisJobRepository";
import { TrainerRepository } from "../domain/repositories/TrainerRepository";
import { CreateAnalysisJobUseCase } from "../use-cases/CreateAnalysisJobUseCase";

// Mock repositories
class MockAnalysisJobRepository implements AnalysisJobRepository {
    private jobs: Map<string, AnalysisJob> = new Map();

    async create(job: any): Promise<void> {
        this.jobs.set(job.captureId, AnalysisJob.fromDocument(job));
    }

    async update(job: any): Promise<void> {
        this.jobs.set(job.captureId, AnalysisJob.fromDocument(job));
    }

    async getByCaptureId(captureId: string): Promise<AnalysisJob | null> {
        return this.jobs.get(captureId) || null;
    }

    async getByTrainerId(trainerId: string, limit?: number, offset?: number): Promise<AnalysisJob[]> {
        return Array.from(this.jobs.values()).filter((job) => job.trainerId === trainerId);
    }

    // Helper method for testing
    clear(): void {
        this.jobs.clear();
    }
}

class MockTrainerRepository implements TrainerRepository {
    private trainers: Map<string, Trainer> = new Map();

    async getById(id: string): Promise<Trainer | null> {
        return this.trainers.get(id) || null;
    }

    async getByIds(ids: string[]): Promise<Trainer[]> {
        return ids.map((id) => this.trainers.get(id)).filter(Boolean) as Trainer[];
    }

    async update(trainer: any): Promise<void> {
        this.trainers.set(trainer.id, new Trainer(trainer));
    }

    async create(trainer: any): Promise<void> {
        this.trainers.set(trainer.id, new Trainer(trainer));
    }

    // Helper method for testing
    addTrainer(trainer: Trainer): void {
        this.trainers.set(trainer.id, trainer);
    }

    clear(): void {
        this.trainers.clear();
    }
}

describe("CreateAnalysisJobUseCase", () => {
    let useCase: CreateAnalysisJobUseCase;
    let mockAnalysisJobRepo: MockAnalysisJobRepository;
    let mockTrainerRepo: MockTrainerRepository;

    const mockDate = new Date("2025-01-01T00:00:00Z");

    beforeEach(() => {
        mockAnalysisJobRepo = new MockAnalysisJobRepository();
        mockTrainerRepo = new MockTrainerRepository();
        useCase = new CreateAnalysisJobUseCase(mockAnalysisJobRepo);

        jest.useFakeTimers();
        jest.setSystemTime(mockDate);
    });

    afterEach(() => {
        jest.useRealTimers();
        mockAnalysisJobRepo.clear();
        mockTrainerRepo.clear();
    });

    describe("execute", () => {
        const validRequest = {
            captureId: "test-capture-id",
            trainerId: "test-trainer-id",
            storageFilePath: "shots/test-trainer-id/test-capture-id/40.7128_-74.0060_test.jpg",
        };

        it("should create an analysis job successfully", async () => {
            // Arrange
            const trainer = new Trainer({
                id: "test-trainer-id",
                username: "TestTrainer",
                pigeonBalls: 5,
                dignityPoints: 0,
                decks: [],
                currentDeckId: null,
            });
            mockTrainerRepo.addTrainer(trainer);

            // Act
            const result = await useCase.execute(validRequest);

            // Assert
            expect(result).toBeInstanceOf(AnalysisJob);
            expect(result.captureId).toBe("test-capture-id");
            expect(result.trainerId).toBe("test-trainer-id");
            expect(result.storageFilePath).toBe("shots/test-trainer-id/test-capture-id/40.7128_-74.0060_test.jpg");
            expect(result.status).toBe(AnalysisJobStatus.PENDING);
            expect(result.errorCode).toBeNull();
            expect(result.errorMessage).toBeNull();
            expect(result.pigeon).toBeNull();
            expect(result.createdAt).toEqual(mockDate);
            expect(result.updatedAt).toEqual(mockDate);

            // Verify job was saved to repository
            const savedJob = await mockAnalysisJobRepo.getByCaptureId("test-capture-id");
            expect(savedJob).toBeTruthy();
            expect(savedJob!.captureId).toBe("test-capture-id");
        });
    });
});
