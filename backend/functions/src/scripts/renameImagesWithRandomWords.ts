import * as fs from 'fs';
import * as path from 'path';
import { generate } from 'random-words';

interface FileInfo {
  originalPath: string;
  extension: string;
  randomWord: string;
}

async function renameImagesWithRandomWords() {
  const tmpDir = path.join(__dirname, '../tmp');
  
  try {
    // Read all files in the tmp directory
    const files = fs.readdirSync(tmpDir);
    
    // Filter only image files (jpg, jpeg, png)
    const imageFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png'].includes(ext) && fs.statSync(path.join(tmpDir, file)).isFile();
    });
    
    console.log(`Found ${imageFiles.length} image files to rename`);
    
    if (imageFiles.length === 0) {
      console.log('No image files found in the tmp directory');
      return;
    }
    
    // Generate random words for each image
    const fileInfos: FileInfo[] = [];
    const usedWords = new Set<string>();
    
    for (const file of imageFiles) {
      const originalPath = path.join(tmpDir, file);
      const extension = path.extname(file);
      
      // Generate a unique random word
      let randomWord: string;
      do {
        randomWord = generate({ exactly: 1, wordsPerString: 1, separator: '' })[0];
      } while (usedWords.has(randomWord));
      
      usedWords.add(randomWord);
      
      fileInfos.push({
        originalPath,
        extension,
        randomWord
      });
    }
    
    // Sort by random word alphabetically
    fileInfos.sort((a, b) => a.randomWord.localeCompare(b.randomWord));
    
    console.log('\nPlanned renames:');
    fileInfos.forEach((info, index) => {
      const originalFileName = path.basename(info.originalPath);
      const newFileName = `${info.randomWord}${info.extension}`;
      console.log(`${index + 1}. ${originalFileName} -> ${newFileName}`);
    });
    
    // Ask for confirmation
    console.log('\nDo you want to proceed with renaming? (y/n)');
    
    // For script execution, we'll proceed automatically
    // In a real scenario, you might want to add readline for user input
    const proceed = true; // Change this to false if you want to review first
    
    if (proceed) {
      console.log('\nRenaming files...');
      
      let successCount = 0;
      let errorCount = 0;
      
      for (const info of fileInfos) {
        try {
          const newPath = path.join(tmpDir, `${info.randomWord}${info.extension}`);
          
          // Check if target file already exists
          if (fs.existsSync(newPath)) {
            console.log(`Warning: Target file ${info.randomWord}${info.extension} already exists, skipping...`);
            errorCount++;
            continue;
          }
          
          fs.renameSync(info.originalPath, newPath);
          console.log(`✓ Renamed to ${info.randomWord}${info.extension}`);
          successCount++;
        } catch (error) {
          console.error(`✗ Error renaming ${path.basename(info.originalPath)}: ${error}`);
          errorCount++;
        }
      }
      
      console.log(`\nRenaming complete!`);
      console.log(`Successfully renamed: ${successCount} files`);
      console.log(`Errors: ${errorCount} files`);
      
      // List the final files in alphabetical order
      console.log('\nFinal files in alphabetical order:');
      const finalFiles = fs.readdirSync(tmpDir)
        .filter(file => {
          const ext = path.extname(file).toLowerCase();
          return ['.jpg', '.jpeg', '.png'].includes(ext) && fs.statSync(path.join(tmpDir, file)).isFile();
        })
        .sort();
      
      finalFiles.forEach((file, index) => {
        console.log(`${index + 1}. ${file}`);
      });
      
    } else {
      console.log('Renaming cancelled.');
    }
    
  } catch (error) {
    console.error('Error reading tmp directory:', error);
  }
}

// Run the script
if (require.main === module) {
  renameImagesWithRandomWords().catch(console.error);
}

export { renameImagesWithRandomWords };
