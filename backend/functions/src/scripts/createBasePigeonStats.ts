/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable max-len */
import * as dotenv from "dotenv";
import * as admin from "firebase-admin";
import * as path from "path";
import { FirestoreBasePigeonRepository } from "../infrastructure/firebase/FirestoreBasePigeonRepository";
dotenv.config({ path: path.resolve(__dirname, "../../.env") });

import { BasePigeonStats } from "../domain/entities/BasePigeonStats";
import { Pigeon, PigeonDocument } from "../domain/entities/Pigeon";
import serviceAccount from "../firebaseServiceAccount";
import { FirestoreBasePigeonStatsRepository } from "../infrastructure/firebase/FirestoreBasePigeonStatsRepository";

admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
});

async function createBasePigeonStats() {
    const basePigeonRepo = new FirestoreBasePigeonRepository();
    const basePigeonStatsRepo = new FirestoreBasePigeonStatsRepository();
    const pigeonCollection = admin.firestore().collection("pigeons");
    const basePigeonStatsCollection = admin.firestore().collection("basePigeonStats");
    await basePigeonStatsCollection.get().then((snapshot) => {
        snapshot.forEach((doc) => {
            doc.ref.delete();
        });
    });

    try {
        const basePigeons = await basePigeonRepo.getAll();
        const allPigeons = await pigeonCollection.get();
        const pigeons = allPigeons.docs.map((doc) => doc.data() as PigeonDocument);

        for (const pidge of pigeons) {
            const pigeon = Pigeon.fromDocument(pidge);
            const basePigeon = basePigeons.find((bp) => bp.id === pigeon.basePigeonId);
            if (!basePigeon) {
                console.error(`Base pigeon not found for pigeon ${pigeon.id}`);
                continue;
            }

            const captureDay = BasePigeonStats.formatDay(pigeon.capturedAt);
            await basePigeonStatsRepo.incrementCapture(captureDay, basePigeon.id, basePigeon.slug);
        }
    } catch (error) {
        console.error("Error adding pigeon:", error);
    } finally {
        await admin.app().delete();
    }
}

createBasePigeonStats();
