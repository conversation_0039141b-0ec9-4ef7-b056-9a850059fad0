/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable max-len */
import * as dotenv from "dotenv";
import * as admin from "firebase-admin";
import * as path from "path";
import { FirestoreBasePigeonRepository } from "../infrastructure/firebase/FirestoreBasePigeonRepository";
dotenv.config({ path: path.resolve(__dirname, "../../.env") });

import serviceAccount from "../firebaseServiceAccount";
// import {defaultBasePigeon} from '../domain/value-objects/BasePigeon';
import { v4 as uuidv4 } from "uuid";
import { MaintenanceServiceImpl } from "../domain/services/MaintenanceService";
import { StatsService } from "../domain/services/StatsService";
import { FirestoreAnalysisJobRepository } from "../infrastructure/firebase/FirestoreAnalysisJobRepository";
import { FirestoreBasePigeonStatsRepository } from "../infrastructure/firebase/FirestoreBasePigeonStatsRepository";
import { FirestoreMaintenanceRepository } from "../infrastructure/firebase/FirestoreMaintenanceRepository";
import { FirestorePigeonRepository } from "../infrastructure/firebase/FirestorePigeonRepository";
import { FirestoreTrainerRepository } from "../infrastructure/firebase/FirestoreTrainerRepository";
import { FirestoreTrainerStatsRepository } from "../infrastructure/firebase/FirestoreTrainerStatsRepository";
import { AdvancedGeoLocationService } from "../infrastructure/geolocation/AdvancedGeoLocationService";
import { OpenAiServiceV2 } from "../infrastructure/openai/OpenAiServiceV2";
import { OpenAiServiceV3 } from "../infrastructure/openai/OpenAiServiceV3";
import { CapturePigeonUseCase } from "../use-cases/CapturePigeonUseCase";
import { GetTrainerPigeonsUseCase } from "../use-cases/GetTrainerPigeonsUseCase";
import { PingExternalServiceUseCase } from "../use-cases/PingExternalServiceUseCase";
import {AnalysisJobStatus} from "../domain/enums/AnalysisJobStatus";
import {AnalysisJob} from "../domain/entities/AnalysisJob";

// Initialisation de Firebase Admin SDK
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
});

async function testCapturePigeon() {
    const basePigeonRepo = new FirestoreBasePigeonRepository();
    const pigeonRepo = new FirestorePigeonRepository();
    const trainerRepo = new FirestoreTrainerRepository();
    const analysisJobRepo = new FirestoreAnalysisJobRepository();
    const analysisService = new OpenAiServiceV2(basePigeonRepo);
    const analysisServiceV3 = new OpenAiServiceV3(basePigeonRepo);
    const geoLocationService = new AdvancedGeoLocationService();
    const getTrainerPigeonsUseCase = new GetTrainerPigeonsUseCase(trainerRepo, pigeonRepo, basePigeonRepo);

    // const analysis = await analysisServiceV3.analyzePigeonPicture(
    //     "https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F6e71e118-9888-4872-8623-f46d5cb31ed4%2Fstandard_12.png?alt=media&token=9f38c6bc-b59a-4574-a2ce-5e69bf51092d",
    // );
    // const analysis = await analyzeService.analyzePigeonPicture(
    //     "https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F6e71e118-9888-4872-8623-f46d5cb31ed4%2Flow_quali_pidge2.png?alt=media&token=def188db-b5f3-4c8c-9ed2-7437cf2b8473",
    // );
    // const analysis = await analysisServiceV3.analyzePigeonPicture(
    //     "https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F6e71e118-9888-4872-8623-f46d5cb31ed4%2F1CE2C55A-FA59-4161-B269-038B3D410338_1_105_c.jpeg?alt=media&token=a02b7162-9932-4e6c-9a8c-e2742b0e390d",
    // );
    // const analysis = await analyzeService.analyzePigeonPicture(
    //     "https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F6e71e118-9888-4872-8623-f46d5cb31ed4%2FCapture%20d%E2%80%99e%CC%81cran%202025-05-10%20a%CC%80%2019.12.56.png?alt=media&token=97f4686b-fb6d-47a9-93c8-dfe2429c7840",
    // );
    // console.log("Analysis:", analysis);
    const pingExternalServiceUseCase = new PingExternalServiceUseCase();
    const statsService = new StatsService();
    const trainerStatsRepo = new FirestoreTrainerStatsRepository();
    const basePigeonStatsRepo = new FirestoreBasePigeonStatsRepository();
    const maintenanceRepo = new FirestoreMaintenanceRepository();
    const maintenanceService = new MaintenanceServiceImpl(maintenanceRepo);
    const capturePigeonUseCase = new CapturePigeonUseCase(
        trainerRepo,
        pigeonRepo,
        analysisService,
        geoLocationService,
        analysisJobRepo,
        pingExternalServiceUseCase,
        statsService,
        trainerStatsRepo,
        basePigeonStatsRepo,
        maintenanceService,
    );

    try {
        const trainerId = "titi";
        // const trainer = await trainerRepo.getById(trainerId);
        const fakeCaptureId = uuidv4();
        const trainerFileStoragePath = `shots/${trainerId}/${fakeCaptureId}/48.12_2.21_rusty.png`;
        const trainerFileUrl = 'https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F1qg2j8v6ajfhd5ad%2F10f77202-bd98-4e23-9110-298c744d56fe%2F48.85664082414928_2.3522247152967526_capture.jpg?alt=media&token=d889d42f-8563-4b52-b3f5-15090318b645';
        const fakeAnalysisJob = new AnalysisJob({
            captureId: fakeCaptureId,
            trainerId,
            storageFilePath: trainerFileStoragePath,
            status: AnalysisJobStatus.PENDING,
            errorCode: null,
            errorMessage: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            pigeon: null,
        });
        await analysisJobRepo.create(fakeAnalysisJob.toDocument());
        await capturePigeonUseCase.execute(trainerFileStoragePath, trainerFileUrl);
    } catch (error) {
        console.error("Error adding pigeon:", error);
    } finally {
        // Close the Firestore connection
        await admin.app().delete();
    }
}

testCapturePigeon();
