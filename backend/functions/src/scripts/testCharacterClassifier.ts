#!/usr/bin/env ts-node

import * as fs from "fs";
import * as path from "path";
import { PigeonCharacterClassifier, PigeonCharacter, PigeonAnalysisResult } from "../domain/services/PigeonCharacterClassifier";

/**
 * Test script to validate the character classification algorithm
 * against the actual analysis results
 */

class CharacterClassifierTester {
    private analysisResults: PigeonAnalysisResult[] = [];
    private characterCounts: Map<PigeonCharacter, number> = new Map();
    private testResults: Array<{
        filename: string;
        character: PigeonCharacter;
        description: string;
        analysis: PigeonAnalysisResult;
    }> = [];

    constructor() {
        this.loadAnalysisResults();
    }

    private loadAnalysisResults(): void {
        const resultsPath = path.join(__dirname, "../tmp/analysis_results.json");
        const rawData = fs.readFileSync(resultsPath, "utf-8");
        this.analysisResults = JSON.parse(rawData);
        console.log(`Loaded ${this.analysisResults.length} analysis results`);
    }

    /**
     * Test the classifier on all analysis results
     */
    testAllResults(): void {
        console.log("\n=== Testing Character Classification Algorithm ===\n");

        for (const analysis of this.analysisResults) {
            const character = PigeonCharacterClassifier.classifyPigeon(analysis);
            const description = PigeonCharacterClassifier.getCharacterDescription(character);
            
            // Count character occurrences
            const currentCount = this.characterCounts.get(character) || 0;
            this.characterCounts.set(character, currentCount + 1);

            this.testResults.push({
                filename: analysis.filename,
                character,
                description,
                analysis
            });
        }

        this.printResults();
    }

    /**
     * Print detailed results and statistics
     */
    private printResults(): void {
        console.log("=== CHARACTER DISTRIBUTION ===\n");
        
        // Sort characters by count (descending)
        const sortedCharacters = Array.from(this.characterCounts.entries())
            .sort((a, b) => b[1] - a[1]);

        let totalCharacters = 0;
        for (const [character, count] of sortedCharacters) {
            console.log(`${character}: ${count} instances`);
            totalCharacters++;
        }

        console.log(`\nTotal unique characters found: ${totalCharacters}`);
        console.log(`Target was ~50 characters`);

        // Show some example classifications
        console.log("\n=== EXAMPLE CLASSIFICATIONS ===\n");
        
        // Show examples of each major category
        const examplesByCategory = new Map<string, PigeonCharacter[]>();
        
        for (const [character] of sortedCharacters) {
            const category = this.getCharacterCategory(character);
            if (!examplesByCategory.has(category)) {
                examplesByCategory.set(category, []);
            }
            examplesByCategory.get(category)!.push(character);
        }

        for (const [category, characters] of examplesByCategory) {
            console.log(`${category}:`);
            for (const character of characters.slice(0, 3)) { // Show first 3 of each category
                const count = this.characterCounts.get(character) || 0;
                const example = this.testResults.find(r => r.character === character);
                if (example) {
                    console.log(`  - ${character} (${count}x): ${example.filename}`);
                    console.log(`    "${example.analysis.description}"`);
                }
            }
            console.log();
        }

        // Show interesting cases
        console.log("=== INTERESTING CASES ===\n");
        
        const interestingCases = [
            PigeonCharacter.BABY_PIGEON,
            PigeonCharacter.DEAD_PIGEON,
            PigeonCharacter.HELMET_PIGEON,
            PigeonCharacter.BALDHEAD_PIGEON,
            PigeonCharacter.RARE_DISTINCTIVENESS,
            PigeonCharacter.MAGPIE,
            PigeonCharacter.WOOD_PIGEON
        ];

        for (const character of interestingCases) {
            const examples = this.testResults.filter(r => r.character === character);
            if (examples.length > 0) {
                console.log(`${character} (${examples.length} instances):`);
                for (const example of examples) {
                    console.log(`  - ${example.filename}: "${example.analysis.description}"`);
                }
                console.log();
            }
        }
    }

    /**
     * Get the category of a character for grouping
     */
    private getCharacterCategory(character: PigeonCharacter): string {
        if ([
            PigeonCharacter.MAGPIE, PigeonCharacter.WOOD_PIGEON, PigeonCharacter.TURTLEDOVE,
            PigeonCharacter.DUCK, PigeonCharacter.CROW, PigeonCharacter.RAVEN,
            PigeonCharacter.SEAGULL, PigeonCharacter.ROOSTER, PigeonCharacter.OTHER_BIRD
        ].includes(character)) {
            return "Non-pigeon species";
        }

        if ([PigeonCharacter.BABY_PIGEON, PigeonCharacter.DEAD_PIGEON].includes(character)) {
            return "Special cases";
        }

        if ([
            PigeonCharacter.BLUE_BAR, PigeonCharacter.BLUE_CHECKER,
            PigeonCharacter.BLUE_T_CHECK, PigeonCharacter.BLUE_BARLESS
        ].includes(character)) {
            return "Basic blue pigeons";
        }

        if ([
            PigeonCharacter.BROWN_BAR, PigeonCharacter.BROWN_CHECKER,
            PigeonCharacter.BROWN_OTHER, PigeonCharacter.BROWN_BARLESS
        ].includes(character)) {
            return "Brown pigeons";
        }

        if ([
            PigeonCharacter.ASH_RED_BAR, PigeonCharacter.ASH_RED_CHECKER,
            PigeonCharacter.ASH_RED_BARLESS
        ].includes(character)) {
            return "Ash-red pigeons";
        }

        if ([
            PigeonCharacter.BLUE_SPREAD_BAR, PigeonCharacter.BLUE_SPREAD_CHECKER,
            PigeonCharacter.BLUE_SPREAD_T_CHECK, PigeonCharacter.BLUE_SPREAD_OTHER
        ].includes(character)) {
            return "Spread pigeons (dark)";
        }

        if ([PigeonCharacter.HELMET_PIGEON, PigeonCharacter.BALDHEAD_PIGEON].includes(character)) {
            return "Head pattern pigeons";
        }

        if (character.toString().includes("PIEBALD")) {
            return "Piebald pigeons";
        }

        return "Rare/unusual combinations";
    }

    /**
     * Generate a summary report
     */
    generateReport(): void {
        const reportPath = path.join(__dirname, "../tmp/character_classification_report.json");
        
        const report = {
            total_images: this.analysisResults.length,
            total_unique_characters: this.characterCounts.size,
            target_characters: 50,
            character_distribution: Object.fromEntries(this.characterCounts),
            examples: this.testResults.slice(0, 20), // First 20 examples
            timestamp: new Date().toISOString()
        };

        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\nDetailed report saved to: ${reportPath}`);
    }
}

// Main execution
async function main() {
    console.log("Starting Character Classification Test...");

    const tester = new CharacterClassifierTester();
    tester.testAllResults();
    tester.generateReport();

    console.log("\nTest completed successfully!");
}

// Run the script if called directly
if (require.main === module) {
    main().catch(console.error);
}
