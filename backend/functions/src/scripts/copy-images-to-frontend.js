const fs = require('fs');
const path = require('path');

function copyImagesToFrontend() {
  console.log('🖼️  Copying pigeon images to frontend...');
  
  // Paths
  const sourceDir = path.join(__dirname, '../tmp');
  const targetDir = path.join(__dirname, '../../../../frontend/desktop/src/assets/pigeon-images');
  const classifierResultsPath = path.join(__dirname, '../tmp/classifier_results.json');
  
  // Create target directory if it doesn't exist
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
    console.log(`📁 Created directory: ${targetDir}`);
  }
  
  // Read classifier results
  const classifierResults = JSON.parse(fs.readFileSync(classifierResultsPath, 'utf8'));
  
  let totalCopied = 0;
  let totalSkipped = 0;
  
  // Process each character group
  Object.entries(classifierResults).forEach(([character, pigeons]) => {
    console.log(`\n🏷️  Processing ${character} (${pigeons.length} pigeons):`);
    
    // Create character subdirectory
    const characterDir = path.join(targetDir, character.toLowerCase().replace(/_/g, '-'));
    if (!fs.existsSync(characterDir)) {
      fs.mkdirSync(characterDir, { recursive: true });
    }
    
    pigeons.forEach((pigeon, index) => {
      const sourceFile = path.join(sourceDir, pigeon.filename);
      const targetFile = path.join(characterDir, pigeon.filename);
      
      if (fs.existsSync(sourceFile)) {
        try {
          fs.copyFileSync(sourceFile, targetFile);
          console.log(`  ✅ ${pigeon.filename}`);
          totalCopied++;
        } catch (error) {
          console.log(`  ❌ Failed to copy ${pigeon.filename}: ${error.message}`);
          totalSkipped++;
        }
      } else {
        console.log(`  ⚠️  Source file not found: ${pigeon.filename}`);
        totalSkipped++;
      }
    });
  });
  
  console.log(`\n📊 Summary:`);
  console.log(`  ✅ Copied: ${totalCopied} images`);
  console.log(`  ⚠️  Skipped: ${totalSkipped} images`);
  console.log(`  📁 Target directory: ${targetDir}`);
  
  // Create an index file for the frontend
  const indexData = {
    totalImages: totalCopied,
    characters: Object.keys(classifierResults).length,
    characterGroups: Object.entries(classifierResults).map(([character, pigeons]) => ({
      character,
      count: pigeons.length,
      directory: character.toLowerCase().replace(/_/g, '-'),
      pigeons: pigeons.map(p => ({
        filename: p.filename,
        distinctiveness: p.analysis.distinctiveness,
        species: p.analysis.species,
        description: p.analysis.description
      }))
    }))
  };
  
  const indexPath = path.join(targetDir, 'index.json');
  fs.writeFileSync(indexPath, JSON.stringify(indexData, null, 2));
  console.log(`\n📄 Created index file: ${indexPath}`);
  
  return indexData;
}

// Run the copying
if (require.main === module) {
  copyImagesToFrontend();
}

module.exports = { copyImagesToFrontend };
