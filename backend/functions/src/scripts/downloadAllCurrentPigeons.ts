import * as fs from "fs";
import * as path from "path";
import * as admin from "firebase-admin";
import "../env";

import serviceAccount from "../firebaseServiceAccount";

// Initialize Firebase Admin SDK
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
});

async function addAllBasePigeons() {
    const pigeonCollection = admin.firestore().collection("pigeons");
    const allPigeons = await pigeonCollection.get();

    const tmpDir = path.resolve(__dirname, "../tmp");
    if (!fs.existsSync(tmpDir)) {
        fs.mkdirSync(tmpDir, { recursive: true });
    }

    for (const pigeon of allPigeons.docs) {
        const url = pigeon.data().originalPicture.originalUrl;

        // On extrait le chemin relatif depuis l'URL
        const { pathname } = new URL(url); // => "/pigeon-gogo.firebasestorage.app/shots/.../img.jpg"
        const objectPath = decodeURIComponent(pathname.replace(/^\/[^/]+\//, "")); // remove "/bucket-name/"

        const destination = path.join(tmpDir, `${pigeon.id}.jpg`);

        await admin.storage().bucket(process.env.FIREBASE_BUCKET).file(objectPath).download({ destination });
    }

    await admin.app().delete();
    console.log("Finished adding all base pigeons");
}

addAllBasePigeons();
