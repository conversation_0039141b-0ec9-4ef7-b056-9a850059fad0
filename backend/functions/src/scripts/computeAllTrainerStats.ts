/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable max-len */
import * as dotenv from "dotenv";
import * as admin from "firebase-admin";
import * as path from "path";
import { FirestoreBasePigeonRepository } from "../infrastructure/firebase/FirestoreBasePigeonRepository";
dotenv.config({ path: path.resolve(__dirname, "../../.env") });

import serviceAccount from "../firebaseServiceAccount";
import { TrainerStats } from "../domain/entities/TrainerStats";
import { FirestorePigeonRepository } from "../infrastructure/firebase/FirestorePigeonRepository";
import { FirestoreTrainerRepository } from "../infrastructure/firebase/FirestoreTrainerRepository";
import { FirestoreTrainerStatsRepository } from "../infrastructure/firebase/FirestoreTrainerStatsRepository";
import { GetTrainerPigeonsUseCase } from "../use-cases/GetTrainerPigeonsUseCase";
import { v4 as uuidv4 } from "uuid";

// Initialisation de Firebase Admin SDK
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
});

async function buildTrainerStats() {
    const basePigeonRepo = new FirestoreBasePigeonRepository();
    const pigeonRepo = new FirestorePigeonRepository();
    const trainerRepo = new FirestoreTrainerRepository();
    const getTrainerPigeonsUseCase = new GetTrainerPigeonsUseCase(trainerRepo, pigeonRepo, basePigeonRepo);
    const trainerStatsRepo = new FirestoreTrainerStatsRepository();

    try {
        // build stats for the whole db

        const trainerCollection = admin.firestore().collection("trainers");
        const trainerStatsCollection = admin.firestore().collection("trainerStats");

        const allTrainers = await trainerCollection.get();

        for (const { id: trainerId } of allTrainers.docs.map((doc) => doc.data())) {
            const trainerStats = await trainerStatsRepo.getByTrainerId(trainerId);
            if (trainerStats) {
                await trainerStatsCollection.doc(trainerStats.id).delete();
            }
            const trainerPigeons = await getTrainerPigeonsUseCase.execute(trainerId, 10000, 0);

            const newTrainerStats = new TrainerStats({
                id: uuidv4(),
                trainerId: trainerId,
                pigeonCount: 0,
                distinctPigeonCount: 0,
                basePigeonList: [],
                legendaryCount: 0,
                epicCount: 0,
                rareCount: 0,
                commonCount: 0,
                totalCatcherPoints: 0,
                battlesWon: 0,
                battlesLost: 0,
                elo: 1000,
            });

            for (const pigeon of trainerPigeons.pigeons) {
                newTrainerStats.capturePigeon(pigeon);
            }

            await trainerStatsRepo.create(newTrainerStats.toDocument());
        }
    } catch (error) {
        console.error("Error adding pigeon:", error);
    } finally {
        // Close the Firestore connection
        await admin.app().delete();
    }
}

buildTrainerStats();
