/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable max-len */
import * as admin from "firebase-admin";
import serviceAccount from "../firebaseServiceAccount";

import { FirestorePigeonRepository } from "../infrastructure/firebase/FirestorePigeonRepository";
import { FirestoreTrainerRepository } from "../infrastructure/firebase/FirestoreTrainerRepository";

admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
});

async function clearTrainerData(trainerId: string, from: Date, to: Date) {
    const trainerRepo = new FirestoreTrainerRepository();
    const pigeonsCollection = admin.firestore().collection("pigeons");
    const analysisCollection = admin.firestore().collection("analysisJobs");
    const trainer = await trainerRepo.getById(trainerId);
    if (!trainer) {
        return;
    }
    const pigeonRepo = new FirestorePigeonRepository();
    const trainerPigeons = await pigeonRepo.getByOwnerId(trainer.id);
    const notValidPigeons = trainerPigeons.filter((p) => p.capturedAt < from || p.capturedAt > to);

    for (const pige of notValidPigeons) {
        const captureId = pige.captureId;
        await pigeonsCollection.doc(pige.id).delete();
        await analysisCollection
            .where("captureId", "==", captureId)
            .get()
            .then((querySnapshot) => {
                querySnapshot.forEach((doc) => {
                    doc.ref.delete();
                });
            });
    }

    try {
    } catch (error) {
        console.error("Error adding pigeon:", error);
    } finally {
        await admin.app().delete();
    }
}

clearTrainerData("2xr1frrhik970moy", new Date("2025-07-11"), new Date("2025-11-01"));
