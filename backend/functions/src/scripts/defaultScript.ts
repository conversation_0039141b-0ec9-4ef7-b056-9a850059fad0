/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable max-len */
import * as dotenv from "dotenv";
import * as admin from "firebase-admin";
import * as path from "path";
import { FirestoreBasePigeonRepository } from "../infrastructure/firebase/FirestoreBasePigeonRepository";
dotenv.config({ path: path.resolve(__dirname, "../../.env") });

import serviceAccount from "../firebaseServiceAccount";
// import {defaultBasePigeon} from '../domain/value-objects/BasePigeon';
import fs from "fs";
import { PigeonRarity } from "../domain/enums/PigeonRarity";
import { StatsService } from "../domain/services/StatsService";
import { FirestoreAnalysisJobRepository } from "../infrastructure/firebase/FirestoreAnalysisJobRepository";
import { FirestorePigeonRepository } from "../infrastructure/firebase/FirestorePigeonRepository";
import { FirestoreTrainerRepository } from "../infrastructure/firebase/FirestoreTrainerRepository";
import { FirestoreTrainerStatsRepository } from "../infrastructure/firebase/FirestoreTrainerStatsRepository";
import { AdvancedGeoLocationService } from "../infrastructure/geolocation/AdvancedGeoLocationService";
import { OpenAiServiceV2 } from "../infrastructure/openai/OpenAiServiceV2";
import { OpenAiServiceV3 } from "../infrastructure/openai/OpenAiServiceV3";
import { CapturePigeonUseCase } from "../use-cases/CapturePigeonUseCase";
import { CreateAnalysisJobUseCase } from "../use-cases/CreateAnalysisJobUseCase";
import { PingExternalServiceUseCase } from "../use-cases/PingExternalServiceUseCase";
import { GetTrainerPigeonsUseCase } from "../use-cases/GetTrainerPigeonsUseCase";
import { GetLeaderBoardUseCase } from "../use-cases/GetLeaderBoardUseCase";
import { TrainerStatsOrderBy } from "../domain/enums/TrainerStatsOrderBy";
import { FirestoreBasePigeonStatsRepository } from "../infrastructure/firebase/FirestoreBasePigeonStatsRepository";
import { GetBasPigeonStatsUseCase } from "../use-cases/GetBasePigeonStatsUseCase";
import { GenerateBasePigeonStatsChartUseCase } from "../use-cases/GenerateBasePigeonStatsChartUseCase";
import { FirestoreMaintenanceRepository } from "../infrastructure/firebase/FirestoreMaintenanceRepository";
import { MaintenanceServiceImpl } from "../domain/services/MaintenanceService";

// Initialisation de Firebase Admin SDK
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
});

async function testCapturePigeon() {
    const basePigeonRepo = new FirestoreBasePigeonRepository();
    const pigeonRepo = new FirestorePigeonRepository();
    const trainerRepo = new FirestoreTrainerRepository();
    const analysisJobRepo = new FirestoreAnalysisJobRepository();
    const analysisService = new OpenAiServiceV2(basePigeonRepo);
    const analysisServiceV3 = new OpenAiServiceV3(basePigeonRepo);
    const geoLocationService = new AdvancedGeoLocationService();
    const getTrainerPigeonsUseCase = new GetTrainerPigeonsUseCase(trainerRepo, pigeonRepo, basePigeonRepo);

    // const analysis = await analysisServiceV3.analyzePigeonPicture(
    //     "https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F6e71e118-**************-f46d5cb31ed4%2Fstandard_12.png?alt=media&token=9f38c6bc-b59a-4574-a2ce-5e69bf51092d",
    // );
    // const analysis = await analyzeService.analyzePigeonPicture(
    //     "https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F6e71e118-**************-f46d5cb31ed4%2Flow_quali_pidge2.png?alt=media&token=def188db-b5f3-4c8c-9ed2-7437cf2b8473",
    // );
    // const analysis = await analysisServiceV3.analyzePigeonPicture(
    //     "https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F6e71e118-**************-f46d5cb31ed4%2F1CE2C55A-FA59-4161-B269-038B3D410338_1_105_c.jpeg?alt=media&token=a02b7162-9932-4e6c-9a8c-e2742b0e390d",
    // );
    // const analysis = await analyzeService.analyzePigeonPicture(
    //     "https://firebasestorage.googleapis.com/v0/b/pigeon-gogo.firebasestorage.app/o/shots%2F6e71e118-**************-f46d5cb31ed4%2FCapture%20d%E2%80%99e%CC%81cran%202025-05-10%20a%CC%80%2019.12.56.png?alt=media&token=97f4686b-fb6d-47a9-93c8-dfe2429c7840",
    // );
    // console.log("Analysis:", analysis);
    const pingExternalServiceUseCase = new PingExternalServiceUseCase();
    const statsService = new StatsService();
    const trainerStatsRepo = new FirestoreTrainerStatsRepository();
    const basePigeonStatsRepo = new FirestoreBasePigeonStatsRepository();
    const maintenanceRepo = new FirestoreMaintenanceRepository();
    const maintenanceService = new MaintenanceServiceImpl(maintenanceRepo);
    const capturePigeonUseCase = new CapturePigeonUseCase(
        trainerRepo,
        pigeonRepo,
        analysisService,
        geoLocationService,
        analysisJobRepo,
        pingExternalServiceUseCase,
        statsService,
        trainerStatsRepo,
        basePigeonStatsRepo,
        maintenanceService,
    );
    const createAnalysisJobUseCase = new CreateAnalysisJobUseCase(analysisJobRepo);

    const getLeaderBoardUseCase = new GetLeaderBoardUseCase(trainerStatsRepo, trainerRepo);
    const getBasePigeonStatsUseCase = new GetBasPigeonStatsUseCase(basePigeonStatsRepo, basePigeonRepo);

    const generateBasePigeonStatsChartUseCase = new GenerateBasePigeonStatsChartUseCase(getBasePigeonStatsUseCase);

    try {
        await generateBasePigeonStatsChartUseCase.execute();
        // const trainerCollection = admin.firestore().collection("trainers");
        // await trainerCollection.doc("1qg2j8v6ajfhd5ad").update({
        //     pigeonBalls: admin.firestore.FieldValue.increment(5),
        // });
        // const rarity = PigeonRarity.LEGENDARY;
        // // const stats = statsService.computeBaseStatsBasedOnRarity(rarity);
        // // const total = stats.health + stats.strength + stats.agility;
        // const statsListWithRarity: any[] = [];
        // for (let i = 0; i < 1000; i++) {
        //     const stats = statsService.computeBaseStatsBasedOnRarity(rarity);
        //     const total = stats.health + stats.strength + stats.agility;
        //     statsListWithRarity.push({ stats, total, rarity });
        // }
        // const statsListWithRaritySorted = statsListWithRarity.sort((a, b) => a.total - b.total);
        // fs.writeFile("stats.json", JSON.stringify(statsListWithRaritySorted, null, 2), "utf8", (err: any) => {
        //     if (err) throw err;
        //     console.log("The file has been saved!");
        // });
        // console.log("Stats:", statsListWithRaritySorted);
        // const storagePath =
        //     "shots/6e71e118-**************-f46d5cb31ed4/f585df5c-72c6-4ef0-8d96-484d6e91411d/48.12_2.21_rusty.png";
        // const signedUrl = (
        //     await admin
        //         .storage()
        //         .bucket("gs://pigeon-gogo.firebasestorage.app")
        //         .file(storagePath)
        //         .getSignedUrl({ action: "read", expires: Date.now() + 1000 * 60 * 60 })
        // )[0];
        // console.log("Signed URL:", signedUrl);
        // // Test with coordinates (New York City) in the file path
        // const latitude = 0.049;
        // const longitude = 0.049;
        // const captureId = uuidv4();
        // const trainerId = "6e71e118-**************-f46d5cb31ed4";
        // const filePath = `shots/${trainerId}/${captureId}/${latitude}_${longitude}_test.jpg`;
        // // First create the analysis job
        // console.log("Creating analysis job...");
        // const analysisJob = await createAnalysisJobUseCase.execute({
        //     captureId,
        //     trainerId,
        //     storageFilePath: filePath,
        // });
        // console.log("Analysis job created:", analysisJob.captureId, analysisJob.status);
        // // Then execute the capture
        // await capturePigeonUseCase.execute(filePath, signedUrl);
        // console.log(`Pigeon captured at coordinates: ${latitude}, ${longitude}`);
        // console.log(
        //     `Pigeon gang: ${geoLocationService.determineGang({ latitude, longitude }, new Date(2025, 0, 1, 32, 0, 0, 0))}`,
        // );
    } catch (error) {
        console.error("Error adding pigeon:", error);
    } finally {
        // Close the Firestore connection
        await admin.app().delete();
    }
}

testCapturePigeon();
