const fs = require('fs');
const path = require('path');

// Import the classifier (we'll need to adapt this for Node.js)
// For now, let's recreate the essential parts

const PigeonCharacter = {
  // Non-pigeon species (9 characters)
  MAGPIE: "MAGPIE",
  WOOD_PIGEON: "WOOD_PIGEON",
  TURTLEDOVE: "TURTLEDOVE",
  DUCK: "DUCK",
  CROW: "CROW",
  RAVEN: "RAVEN",
  SEAGULL: "SEAGULL",
  ROOSTER: "ROOSTER",
  OTHER_BIRD: "OTHER_BIRD",

  // Special cases (2 characters)
  BABY_PIGEON: "BABY_PIGEON",
  DEAD_PIGEON: "DEAD_PIGEON",

  // Basic blue pigeons (4 characters)
  BLUE_BAR: "BLUE_BAR",
  BLUE_CHECKER: "BLUE_CHECKER",
  BLUE_T_CHECK: "BLUE_T_CHECK",
  BLUE_BARLESS: "BLUE_BARLESS",

  // Brown pigeons (4 characters)
  BROWN_BAR: "BROWN_BAR",
  BROWN_CHECKER: "BROWN_CHECKER",
  BROWN_OTHER: "BROWN_OTHER",
  BROWN_BARLESS: "BROWN_BARLESS",

  // Ash-red pigeons (3 characters)
  ASH_RED_BAR: "ASH_RED_BAR",
  ASH_RED_CHECKER: "ASH_RED_CHECKER",
  ASH_RED_BARLESS: "ASH_RED_BARLESS",

  // Spread pigeons (dark/black) (4 characters)
  BLUE_SPREAD_BAR: "BLUE_SPREAD_BAR",
  BLUE_SPREAD_CHECKER: "BLUE_SPREAD_CHECKER",
  BLUE_SPREAD_T_CHECK: "BLUE_SPREAD_T_CHECK",
  BLUE_SPREAD_OTHER: "BLUE_SPREAD_OTHER",

  // Head pattern pigeons (2 characters)
  HELMET_PIGEON: "HELMET_PIGEON",
  BALDHEAD_PIGEON: "BALDHEAD_PIGEON",

  // Piebald pigeons (12 characters)
  BLUE_BAR_LIGHT_PIEBALD: "BLUE_BAR_LIGHT_PIEBALD",
  BLUE_BAR_HEAVY_PIEBALD: "BLUE_BAR_HEAVY_PIEBALD",
  BLUE_CHECKER_LIGHT_PIEBALD: "BLUE_CHECKER_LIGHT_PIEBALD",
  BLUE_CHECKER_HEAVY_PIEBALD: "BLUE_CHECKER_HEAVY_PIEBALD",
  BROWN_LIGHT_PIEBALD: "BROWN_LIGHT_PIEBALD",
  BROWN_HEAVY_PIEBALD: "BROWN_HEAVY_PIEBALD",
  BLUE_SPREAD_LIGHT_PIEBALD: "BLUE_SPREAD_LIGHT_PIEBALD",
  BLUE_SPREAD_HEAVY_PIEBALD: "BLUE_SPREAD_HEAVY_PIEBALD",
  MOTTLED_PIEBALD: "MOTTLED_PIEBALD",
  WHITE_PATCHES_PIEBALD: "WHITE_PATCHES_PIEBALD",
  SMALL_WHITE_SPOTS_PIEBALD: "SMALL_WHITE_SPOTS_PIEBALD",
  EXTREME_PIEBALD: "EXTREME_PIEBALD",

  // Rare/unusual combinations (10 characters)
  BLUE_OTHER_PATTERN: "BLUE_OTHER_PATTERN",
  WEIRD_FACE_SPOTS: "WEIRD_FACE_SPOTS",
  MOTTLED_FACE: "MOTTLED_FACE",
  HIGH_IRIDESCENCE: "HIGH_IRIDESCENCE",
  NO_IRIDESCENCE: "NO_IRIDESCENCE",
  WHITE_WING_TIPS: "WHITE_WING_TIPS",
  WHITE_TAIL: "WHITE_TAIL",
  MIXED_TAIL_COLOR: "MIXED_TAIL_COLOR",
  OTHER_BASE_COLOR: "OTHER_BASE_COLOR",
  RARE_DISTINCTIVENESS: "RARE_DISTINCTIVENESS",
};

class PigeonCharacterClassifier {
  static classifyPigeon(analysis) {
    // 1. Check if it's not a bird
    if (!analysis.is_bird) {
      return PigeonCharacter.OTHER_BIRD;
    }

    // 2. Check for non-pigeon species first
    if (analysis.species !== "columba_livia") {
      switch (analysis.species) {
        case "magpie":
          return PigeonCharacter.MAGPIE;
        case "wood_pigeon":
          return PigeonCharacter.WOOD_PIGEON;
        case "turtledove":
          return PigeonCharacter.TURTLEDOVE;
        case "duck":
          return PigeonCharacter.DUCK;
        case "crow":
          return PigeonCharacter.CROW;
        case "raven":
          return PigeonCharacter.RAVEN;
        case "seagull":
        case "gull":
          return PigeonCharacter.SEAGULL;
        case "rooster":
        case "hen":
          return PigeonCharacter.ROOSTER;
        default:
          return PigeonCharacter.OTHER_BIRD;
      }
    }

    // 3. Check for special cases
    if (analysis.is_baby_pigeon) {
      return PigeonCharacter.BABY_PIGEON;
    }

    if (analysis.is_dead) {
      return PigeonCharacter.DEAD_PIGEON;
    }

    // 4. From here on, we're dealing with columba_livia
    const traits = analysis.pigeon_traits;
    if (!traits) {
      return PigeonCharacter.BLUE_BAR; // Default fallback
    }

    // 5. Check for distinctive head patterns first (very rare and distinctive)
    if (traits.head_pattern === "helmet") {
      return PigeonCharacter.HELMET_PIGEON;
    }
    if (traits.head_pattern === "baldhead") {
      return PigeonCharacter.BALDHEAD_PIGEON;
    }

    // 6. Check for rare distinctiveness (curly feathers, etc.)
    if (analysis.distinctiveness === "rare") {
      return PigeonCharacter.RARE_DISTINCTIVENESS;
    }

    // 7. Check for special face patterns
    if (traits.face_pattern === "weird_spots") {
      return PigeonCharacter.WEIRD_FACE_SPOTS;
    }
    if (traits.face_pattern === "mottled") {
      return PigeonCharacter.MOTTLED_FACE;
    }

    // 8. Check for extreme iridescence levels
    if (traits.iridescence_level === "high") {
      return PigeonCharacter.HIGH_IRIDESCENCE;
    }
    if (traits.iridescence_level === "none") {
      return PigeonCharacter.NO_IRIDESCENCE;
    }

    // 9. Check for unusual wing/tail colors
    if (traits.wing_tip_color === "white") {
      return PigeonCharacter.WHITE_WING_TIPS;
    }
    if (traits.tail_color === "white") {
      return PigeonCharacter.WHITE_TAIL;
    }
    if (traits.tail_color === "mixed") {
      return PigeonCharacter.MIXED_TAIL_COLOR;
    }

    // 10. Check for other base color
    if (traits.base_color === "other") {
      return PigeonCharacter.OTHER_BASE_COLOR;
    }

    // 11. Handle spread mutations (creates very dark pigeons)
    if (traits.is_spread && traits.spread_level === "full") {
      if (traits.is_piebald) {
        if (traits.piebald_level === "heavy") {
          return PigeonCharacter.BLUE_SPREAD_HEAVY_PIEBALD;
        } else {
          return PigeonCharacter.BLUE_SPREAD_LIGHT_PIEBALD;
        }
      }

      switch (traits.main_pattern) {
        case "bar":
          return PigeonCharacter.BLUE_SPREAD_BAR;
        case "checker":
          return PigeonCharacter.BLUE_SPREAD_CHECKER;
        case "t-check":
          return PigeonCharacter.BLUE_SPREAD_T_CHECK;
        default:
          return PigeonCharacter.BLUE_SPREAD_OTHER;
      }
    }

    // 12. Handle piebald patterns (white markings)
    if (traits.is_piebald && traits.piebald_level !== "none") {
      // Check for extreme piebald first
      if (
        traits.piebald_level === "full_white" ||
        (traits.piebald_intensity?.head === "fully_white" && traits.piebald_intensity?.body === "fully_white")
      ) {
        return PigeonCharacter.EXTREME_PIEBALD;
      }

      // Check piebald pattern type
      if (traits.piebald_pattern === "mottled") {
        return PigeonCharacter.MOTTLED_PIEBALD;
      }
      if (traits.piebald_pattern === "white_patches") {
        return PigeonCharacter.WHITE_PATCHES_PIEBALD;
      }
      if (traits.piebald_pattern === "small_white_spots") {
        return PigeonCharacter.SMALL_WHITE_SPOTS_PIEBALD;
      }

      // Base color + pattern + piebald level combinations
      if (traits.base_color === "brown") {
        return traits.piebald_level === "heavy"
          ? PigeonCharacter.BROWN_HEAVY_PIEBALD
          : PigeonCharacter.BROWN_LIGHT_PIEBALD;
      }

      if (traits.base_color === "blue") {
        if (traits.main_pattern === "bar") {
          return traits.piebald_level === "heavy"
            ? PigeonCharacter.BLUE_BAR_HEAVY_PIEBALD
            : PigeonCharacter.BLUE_BAR_LIGHT_PIEBALD;
        }
        if (traits.main_pattern === "checker") {
          return traits.piebald_level === "heavy"
            ? PigeonCharacter.BLUE_CHECKER_HEAVY_PIEBALD
            : PigeonCharacter.BLUE_CHECKER_LIGHT_PIEBALD;
        }
      }
    }

    // 13. Handle base color and pattern combinations (standard pigeons)
    switch (traits.base_color) {
      case "brown":
        switch (traits.main_pattern) {
          case "bar":
            return PigeonCharacter.BROWN_BAR;
          case "checker":
            return PigeonCharacter.BROWN_CHECKER;
          case "barless":
            return PigeonCharacter.BROWN_BARLESS;
          default:
            return PigeonCharacter.BROWN_OTHER;
        }

      case "ash-red":
        switch (traits.main_pattern) {
          case "bar":
            return PigeonCharacter.ASH_RED_BAR;
          case "checker":
            return PigeonCharacter.ASH_RED_CHECKER;
          case "barless":
            return PigeonCharacter.ASH_RED_BARLESS;
          default:
            return PigeonCharacter.ASH_RED_BAR; // Default to bar
        }

      case "blue":
      default:
        switch (traits.main_pattern) {
          case "bar":
            return PigeonCharacter.BLUE_BAR;
          case "checker":
            return PigeonCharacter.BLUE_CHECKER;
          case "t-check":
            return PigeonCharacter.BLUE_T_CHECK;
          case "barless":
            return PigeonCharacter.BLUE_BARLESS;
          case "other":
            return PigeonCharacter.BLUE_OTHER_PATTERN;
          default:
            return PigeonCharacter.BLUE_BAR; // Default fallback
        }
    }
  }
}

// Main processing function
function processClassifierResults() {
  console.log('🔄 Processing pigeon classifier results...');
  
  // Read the analysis results
  const analysisPath = path.join(__dirname, '../tmp/analysis_results.json');
  const analysisData = JSON.parse(fs.readFileSync(analysisPath, 'utf8'));
  
  console.log(`📊 Found ${analysisData.length} pigeons to classify`);
  
  // Process each pigeon and classify it
  const classificationResults = {};
  const characterCounts = {};
  
  analysisData.forEach((pigeon, index) => {
    console.log(`🔍 Processing ${index + 1}/${analysisData.length}: ${pigeon.filename}`);
    
    // Classify the pigeon
    const character = PigeonCharacterClassifier.classifyPigeon(pigeon);
    
    // Initialize character group if it doesn't exist
    if (!classificationResults[character]) {
      classificationResults[character] = [];
      characterCounts[character] = 0;
    }
    
    // Add pigeon to the character group
    classificationResults[character].push({
      filename: pigeon.filename,
      character: character,
      analysis: pigeon
    });
    
    characterCounts[character]++;
  });
  
  // Save the classification results
  const outputPath = path.join(__dirname, '../tmp/classifier_results.json');
  fs.writeFileSync(outputPath, JSON.stringify(classificationResults, null, 2));
  
  // Save character counts
  const countsPath = path.join(__dirname, '../tmp/character_counts.json');
  fs.writeFileSync(countsPath, JSON.stringify(characterCounts, null, 2));
  
  console.log('\n📈 Classification Summary:');
  Object.entries(characterCounts)
    .sort(([,a], [,b]) => b - a)
    .forEach(([character, count]) => {
      console.log(`  ${character}: ${count} pigeons`);
    });
  
  console.log(`\n✅ Results saved to:`);
  console.log(`  📄 ${outputPath}`);
  console.log(`  📊 ${countsPath}`);
  
  return classificationResults;
}

// Run the processing
if (require.main === module) {
  processClassifierResults();
}

module.exports = { processClassifierResults, PigeonCharacterClassifier };
