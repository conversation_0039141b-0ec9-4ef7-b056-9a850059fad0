/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable max-len */
import * as admin from "firebase-admin";
import serviceAccount from "../firebaseServiceAccount";

import { FirestoreTrainerRepository } from "../infrastructure/firebase/FirestoreTrainerRepository";
import { Trainer } from "../domain/entities/Trainer";

admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
});

async function createTrainer() {
    const trainerRepo = new FirestoreTrainerRepository();
    try {
        const trainer = new Trainer({
            id: "titi",
            username: "titi",
            pigeonBalls: [],
            dignityPoints: 100,
            decks: [],
            currentDeckId: null,
        });
        await trainerRepo.create(trainer.toDocument());
        console.log("Trainer created");
    } catch (error) {
        console.error("Error adding pigeon:", error);
    } finally {
        await admin.app().delete();
    }
}

createTrainer();
