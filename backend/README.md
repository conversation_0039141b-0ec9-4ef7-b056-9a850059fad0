# Pigeon Go Backend

The backend for Pigeon Go, a location-based mobile game where players capture and battle with pigeons.

## Architecture

The backend is built using Firebase Cloud Functions with TypeScript, following a clean architecture approach:

### Layers

- **Domain**: Contains the core business logic, entities, and interfaces
  - Entities: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Item
  - Value Objects: PigeonAnalysis, Stats, BasePigeon
  - Enums: PigeonGang, PigeonRarity, BirdType, PigeonAppearance
  - Repositories: Interfaces for data access
  - Services: Interfaces for external services

- **Use Cases**: Contains the application-specific business rules
  - CapturePigeonUseCase: Handles the logic for capturing pigeons
  - GetTrainerPigeonsUseCase: Retrieves pigeons for a trainer

- **Infrastructure**: Contains implementations of interfaces defined in the domain layer
  - Firebase: Repository implementations using Firestore
  - OpenAI: AI service implementation for pigeon analysis
  - Geolocation: Services for determining pigeon gang based on coordinates

## Key Features

### Pigeon Capture System

When a user takes a photo of a pigeon:
1. The photo is uploaded to Firebase Storage
2. A Cloud Function is triggered
3. The image is analyzed using OpenAI to determine pigeon characteristics
4. The pigeon's gang is determined based on capture location and time
5. A new pigeon is created and added to the user's collection

### Pigeon Gang System

Pigeons belong to one of five gangs based on the geographic area where they were captured:

- The world is divided into squares of 0.05° latitude/longitude
- Each square is further divided into 64 smaller pieces (8x8 grid)
- Gang ONE: top-left quarter
- Gang TWO: top-right quarter
- Gang THREE: bottom-left quarter
- Gang FOUR: bottom-right quarter
- Gang FIVE follows a 64-entry cycle that repeats every 192 hours (8 days)

## Setup and Development

### Prerequisites

- Node.js (v22)
- Firebase CLI
- OpenAI API key

### Installation

1. Install dependencies:
   ```
   cd functions
   npm install
   ```

2. Create a `.env` file in the `functions` directory with your OpenAI API key:
   ```
   OPENAI_API_KEY=your_api_key_here
   ```

3. Create a `firebaseServiceAccount.ts` file in the `functions/src` directory with your Firebase service account credentials.

### Local Development

Run the Firebase emulator:
```
npm run serve
```

### Testing

Run tests:
```
npm test
```

## Deployment

Deploy to Firebase:
```
npm run deploy
```

## API Endpoints

### Cloud Functions

- **onFileUpload**: Triggered when a file is uploaded to the `shots/` directory in Firebase Storage
  - Format: `shots/<trainerId>/<latitude>_<longitude>_<fileName>.jpg`
  - Processes the image and captures a pigeon

- **getTrainerPigeons**: HTTP callable function to retrieve a trainer's pigeons

## Database Structure

### Firestore Collections

- **trainers**: Contains trainer information
- **pigeons**: Contains captured pigeons
- **basePigeons**: Contains base pigeon templates

## Scripts

- **defaultScript.ts**: Test script for pigeon capture
- **addBasePigeons.ts**: Script to add base pigeons to the database
